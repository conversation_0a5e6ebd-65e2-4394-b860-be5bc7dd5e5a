<template>
	<view class="sunui-tabbar" :class="[fixed ? 'fixed' : '']">
		<view class="tablist flexbox flex_alignc sunui-bottom"
			:style="[{ 'background-color': backgroundColor ? backgroundColor : '' }]">
			<block v-for="(item, index) in tabList" :key="index">
				<view class="navigator flex-column-center" :class="current == index ? 'on' : ''"
					@tap="switchTab(index,item)">
					<block v-if="item.name == '一键分身' && cloneSet.separation_swich == 1">
						<image class="icon-fen" :src="item.icoActive"></image>
					</block>
					<block v-else>
						<image style="width: 64rpx;height: 64rpx;" v-if="current == index" :src="item.icoActive">
						</image>
						<image style="width: 64rpx;height: 64rpx;" v-if="current != index" :src="item.ico"></image>
					</block>
					<view class="text name-text" :style="[current == index ? { color: tintColor } : { color: color }]">
						{{ item.name }}
					</view>
				</view>
			</block>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				tabList: [],
				tabList1: [{
						ico: this.$imgUrl + 'tabbar/8.png',
						icoActive: this.$imgUrl + 'tabbar/9.png',
						name: '首页',
						url: '/pages/index/index'
					},
					{
						ico: this.$imgUrl + 'tabbar/12.png',
						icoActive: this.$imgUrl + 'tabbar/13.png',
						name: '资产',
						url: '/pages/assets/assets?sign=1'
					},
					{
						ico: this.$imgUrl + 'tabbar/5.png',
						icoActive: this.$imgUrl + 'tabbar/7.png',
						name: '作品',
						url: '/pages/works/works?sign=1'
					},
					{
						ico: this.$imgUrl + 'tabbar/10.png',
						icoActive: this.$imgUrl + 'tabbar/11.png',
						name: '我的',
						url: '/pages/my/my?index=3'
					},
				],

				tabList4: [{
						ico: this.$imgUrl + 'tabbar/n-1.png',
						icoActive: this.$imgUrl + 'tabbar/n-1-2.png',
						name: '首页',
						url: '/pages/index/index'
					},
					// {
					// 	ico: this.$imgUrl + 'tabbar/n-2.png',
					// 	icoActive: this.$imgUrl + 'tabbar/n-2-2.png',
					// 	name: '案例',
					// 	url: '/pages/assets/modelList'
					// },
					// {ico: this.$imgUrl+'tabbar/99.png',icoActive: this.$imgUrl+'tabbar/99.png',name: '一键分身',url: '/pages/index/synthesis/synthesis'},
					{
						ico: this.$imgUrl + 'tabbar/n-3.png',
						icoActive: this.$imgUrl + 'tabbar/n-3-2.png',
						name: '作品',
						url: '/pages/assets/digital-assets?sign=1'
					},
					{
						ico: this.$imgUrl + 'tabbar/n-4.png',
						icoActive: this.$imgUrl + 'tabbar/n-4-2.png',
						name: '我的',
						url: '/pages/my/my?index=3'
					},
				],

				tabList5: [{
						ico: this.$imgUrl + 'tabbar/14.png',
						icoActive: this.$imgUrl + 'tabbar/15.png',
						name: '首页',
						url: '/pages/index/index'
					},
					{
						ico: this.$imgUrl + 'tabbar/19.png',
						icoActive: this.$imgUrl + 'tabbar/20.png',
						name: '资产',
						url: '/pages/assets/assets?sign=1'
					},
					{
						ico: this.$imgUrl + 'tabbar/21.png',
						icoActive: this.$imgUrl + 'tabbar/22.png',
						name: '作品',
						url: '/pages/works/works?sign=1'
					},
					{
						ico: this.$imgUrl + 'tabbar/17.png',
						icoActive: this.$imgUrl + 'tabbar/18.png',
						name: '我的',
						url: '/pages/my/my?index=3'
					},
				],

				tabList6: [{
						ico: this.$imgUrl + 'tabbar/14.png',
						icoActive: this.$imgUrl + 'tabbar/15.png',
						name: '首页',
						url: '/pages/index/index'
					},
					{
						ico: this.$imgUrl + 'tabbar/19.png',
						icoActive: this.$imgUrl + 'tabbar/20.png',
						name: '资产',
						url: '/pages/assets/assets?sign=1'
					},
					{
						ico: this.$imgUrl + 'tabbar/21.png',
						icoActive: this.$imgUrl + 'tabbar/22.png',
						name: '作品',
						url: '/pages/works/works?sign=1'
					},
					{
						ico: this.$imgUrl + 'tabbar/17.png',
						icoActive: this.$imgUrl + 'tabbar/18.png',
						name: '我的',
						url: '/pages/my/my?index=3'
					},
				],

				tabList2: [{
						ico: this.$imgUrl + 'tabbar/8.png',
						icoActive: this.$imgUrl + 'tabbar/9.png',
						name: '首页',
						url: '/pages/index/index'
					},
					{
						ico: this.$imgUrl + 'tabbar/207.png',
						icoActive: this.$imgUrl + 'tabbar/208.png',
						name: '合伙人',
						url: '/pages/my/partner/index'
					},
					{
						ico: this.$imgUrl + 'tabbar/205.png',
						icoActive: this.$imgUrl + 'tabbar/206.png',
						name: '卡密商城',
						url: '/pages/my/partner/shop'
					},
					{
						ico: this.$imgUrl + 'tabbar/209.png',
						icoActive: this.$imgUrl + 'tabbar/210.png',
						name: '订单列表',
						url: '/pages/my/partner/order'
					},
				],

				tabList3: [{
						ico: this.$imgUrl + 'tabbar/8.png',
						icoActive: this.$imgUrl + 'tabbar/9.png',
						name: '首页',
						url: '/pages/index/index'
					},
					{
						ico: this.$imgUrl + 'tabbar/201.png',
						icoActive: this.$imgUrl + 'tabbar/202.png',
						name: 'AI文案',
						url: '/pages/index/AICreation/AICreation'
					},
					{
						ico: this.$imgUrl + 'tabbar/203.png',
						icoActive: this.$imgUrl + 'tabbar/204.png',
						name: '记录',
						url: '/pages/index/AICreation/record'
					},
				],

				platform: '',

				cloneSet: '',

			};
		},
		name: 'sunui-tabbar',
		props: {
			current: {
				type: [Number, String],
				default: 0
			},
			types: {
				type: [Number, String],
				default: 1
			},
			backgroundColor: {
				type: String,
				default: '#2E324D'
			},
			color: {
				type: String,
				default: 'rgba(255,255,255,.4)'
			},
			tintColor: {
				type: String,
				default: '#FFF'
			},
			fixed: {
				type: [Boolean, String],
				default: false
			}
		},
		created() {
			uni.getSystemInfo({
				success: (res) => {
					this.platform = res.platform;
				}
			});
			// 客服设置接口
			const getVideoOpen = async () => {
				const resolu = await this.$http.get({
					url: this.$api.getVideoOpen
				})
				if (resolu.errno == 0) {
					console.log(resolu);
					uni.setStorageSync('videoOpen', resolu.data)
					console.log(typeof uni.getStorageSync('videoOpen'));
					if (!uni.getStorageSync('videoOpen')) {
						return
					}
					this.tabList4 = [{
							ico: this.$imgUrl + 'tabbar/n-1.png',
							icoActive: this.$imgUrl + 'tabbar/n-1-2.png',
							name: '首页',
							url: '/pages/index/index'
						},
						{
							ico: this.$imgUrl + 'tabbar/n-2.png',
							icoActive: this.$imgUrl + 'tabbar/n-2-2.png',
							name: '案例',
							url: '/pages/assets/modelList'
						},
						// {ico: this.$imgUrl+'tabbar/99.png',icoActive: this.$imgUrl+'tabbar/99.png',name: '一键分身',url: '/pages/index/synthesis/synthesis'},
						{
							ico: this.$imgUrl + 'tabbar/n-3.png',
							icoActive: this.$imgUrl + 'tabbar/n-3-2.png',
							name: '作品',
							url: '/pages/assets/digital-assets?sign=1'
						},
						{
							ico: this.$imgUrl + 'tabbar/n-4.png',
							icoActive: this.$imgUrl + 'tabbar/n-4-2.png',
							name: '我的',
							url: '/pages/my/my?index=3'
						},
					]
				} else {
					this.$sun.toast(resolu.message, "none")
				}
			}

			getVideoOpen();
			this.getIndexSet();
		},
		methods: {
			switchTab(index, obj) {
				this.$emit('click', index);
				uni.redirectTo({
					url: obj.url
				});

			},
			//首页模板
			async getIndexSet() {
				const result = await this.$http.post({
					url: this.$api.indexSet
				});
				if (result.errno == 0) {
					let template_type = result.data.template_type;
					this.cloneSet = uni.getStorageSync("cloneSet");

					if (this.cloneSet.separation_swich != 1) {
						this.tabList4.splice(2, 1);
					}

					if (this.types == 1) {

						// let template_type = uni.getStorageSync("indexSet");

						if (template_type == 2 || template_type == 3) {
							this.tabList = this.tabList4;
						}
						if (template_type == 1) {
							this.tabList = this.tabList1;
						}
						if (template_type == 4) {
							this.tabList = this.tabList5;
							this.tintColor = '#77FDB1';
						}
						if (template_type == 5) {
							this.tabList = this.tabList6;
							this.tintColor = '#77FDB1';
						}


						// this.tintColor = '#E496FD';
						// this.backgroundColor = '#1B1B1B';
					}
					if (this.types == 2) {
						this.tabList = this.tabList2;
						// this.tintColor = '#23E9FF';
						// this.backgroundColor = '#323232';
					}
					if (this.types == 3) {
						this.tabList = this.tabList3;
						// this.tintColor = '#A3C3FF';
						// this.backgroundColor = '#1B1B1B';
					}
				}
			},
		}
	};
</script>
<style scoped>
	/* view {
		line-height: 1.5;
	} */

	.icon-fen {
		width: 80rpx;
		height: 80rpx;
		border-radius: 100rpx;
		margin-top: -20rpx;
		margin-bottom: 6rpx;
	}

	.name-text {
		/* width: 120rpx; */
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		text-align: center;
	}

	.flexbox {
		display: flex;
	}

	.sunui-position {
		position: relative;
	}

	.sunui-badge {
		background-color: #ff3e3e;
		border-radius: 50upx;
		box-sizing: border-box;
		color: #fff;
		font-size: 12px;
		font-family: arial;
		padding: 6upx 12upx;
		line-height: 1.08;
	}

	.sunui-badge_dot {
		border-radius: 100%;
		font-size: 0;
		overflow: hidden;
		padding: 0;
		height: 18upx;
		width: 18upx;
	}

	.sunui-bottom,
	.sunui-bottombar {
		position: relative;
	}

	.sunui-tabbar {
		display: flex;
		width: 100%;
	}

	.sunui-tabbar .tablist {
		position: relative;
		z-index: 900;
		padding: 16rpx 34rpx;
		width: 718rpx;
		height: 136rpx;
		background: #2E324D;
		border-radius: 32rpx 32rpx 32rpx 32rpx;
		margin: 0 auto;
	}

	.sunui-tabbar .tablist.sunui-bottom:before {
		background: #bbb;
	}

	.sunui-tabbar.fixed {
		padding-top: 150upx;
	}

	.sunui-tabbar.fixed .tablist {
		position: fixed;
		bottom: 16rpx;
		left: 0;
		right: 0;
	}

	.sunui-tabbar .tablist .navigator {
		flex: 1;
		text-align: center;
	}

	.sunui-tabbar .tablist .icon {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto;
		margin-top: 10upx;
		height: 50upx;
		width: 50upx;
		position: relative;
	}

	.sunui-tabbar .tablist .icon .iconfont {
		color: #D9D9D9;
		font-size: 44upx;
	}

	.sunui-tabbar .tablist .text {
		color: #D9D9D9;
		font-size: 28upx;
	}

	.sunui-tabbar .tablist .navigator.on .icon .iconfont {
		color: #ff592e;
	}

	.sunui-tabbar .tablist .navigator.on .text {
		color: #ff592e;
	}

	.sunui-tabbar .tablist .sunui-badge {
		position: absolute;
		top: -3upx;
		left: 32upx;
	}

	.sunui-tabbar .tablist .sunui-badge_dot {
		left: 36upx;
	}
</style>