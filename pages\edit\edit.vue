<template>
	<view>
		<view class="h_20rpx"></view>

		<view v-if="videoList.length == 0" class="display-ac-jc top-sel" @click="getSelVideo()">
			<image class="img-215" :src="imgUrl+'215.png'"></image>
			<view style="color: #69F8AA;">添加视频</view>
		</view>
		<view v-else class="top-swiper">
			<view class="again-sel" @click="getSelVideo()">重新选择</view>
			<view class="bg">
				<view class="placeholder"></view>
				<view class="image">
					<image :src="videoList[swiper.index].result_cover" mode="widthFix"></image>
				</view>
			</view>
			<view class="box">
				<swiper class="swiper" :previous-margin="swiper.margin" :next-margin='swiper.margin' :circular="true"
					@change="swiperChange">
					<swiper-item v-for="(item,index) in videoList" :key="index">
						<!-- <image @click="confirmImage(item)" :class="swiper.index == index ? 'img-345' : 'img-346'" :src="video_id.indexOf(item.id) == -1 ? imgUrl+'89.png' : imgUrl+'346.png'"></image> -->
						<image @click="delVideo(index)" :class="swiper.index == index ? 'img-345' : 'img-346'"
							:src="imgUrl+'60.png'"></image>
						<image v-if="(swiper.index == index) && modelImgs.length > 0" class='model-img'
							:src='modelImgs[modelImgs.length-1]'></image>
						<!-- <video v-if="swiper.index == index" class="video-result" :src="item.result"></video> -->
						<image class='le-img' :src='item.result_cover' :class="{'le-active':swiper.index == index}">
						</image>
						<!-- <image v-else class='le-img' :src='item.result_cover'></image> -->
					</swiper-item>
				</swiper>
			</view>
		</view>

		<view class="display-a e-top">
			<view class="width_125rpx-center display-ac-jc" @click="getTopType(1)">
				<view class="padding-bottom_20rpx" :class="topType == 1 ? 'color_90FE8B' : 'color_B7B7B7'">标题</view>
				<view :class="topType == 1 ? 'e-line-1' : 'e-line'"></view>
			</view>
			<view class="width_125rpx-center display-ac-jc" @click="getTopType(6)">
				<view class="padding-bottom_20rpx" :class="topType == 6 ? 'color_90FE8B' : 'color_B7B7B7'">文案</view>
				<view :class="topType == 6 ? 'e-line-1' : 'e-line'"></view>
			</view>
			<view style="position: relative;" class="width_125rpx-center display-ac-jc" @click="getTopType(2)">
				<view class="model-size" v-if="modelIds.length > 0">{{modelIds.length}}</view>
				<view class="padding-bottom_20rpx" :class="topType == 2 ? 'color_90FE8B' : 'color_B7B7B7'">模板</view>
				<view :class="topType == 2 ? 'e-line-1' : 'e-line'"></view>
			</view>
			<view style="position: relative;" class="width_125rpx-center display-ac-jc" @click="getTopType(3)">
				<view class="model-size" v-if="background_music.length > 0">{{background_music.length}}</view>
				<view class="padding-bottom_20rpx" :class="topType == 3 ? 'color_90FE8B' : 'color_B7B7B7'">音乐</view>
				<view :class="topType == 3 ? 'e-line-1' : 'e-line'"></view>
			</view>
			<view class="width_125rpx-center display-ac-jc" @click="getTopType(4)">
				<view class="padding-bottom_20rpx" :class="topType == 4 ? 'color_90FE8B' : 'color_B7B7B7'">智剪</view>
				<view :class="topType == 4 ? 'e-line-1' : 'e-line'"></view>
			</view>
			<view class="width_125rpx-center display-ac-jc" @click="getTopType(5)">
				<view class="padding-bottom_20rpx" :class="topType == 5 ? 'color_90FE8B' : 'color_B7B7B7'">背景</view>
				<view :class="topType == 5 ? 'e-line-1' : 'e-line'"></view>
			</view>
		</view>

		<block v-if="topType == 1">
			<view class="color_FFFFFF" style="padding: 30rpx 20rpx;">
				<view class="display-a-js margin-bottom_20rpx">
					<view class="font-size_32rpx">标题显示</view>
					<!-- <switch @change="titleSwitchChange" :checked="title_swich" color="#43FC8B" style="transform:scale(0.9)"/> -->
				</view>
				<block v-if="title_swich">
					<view class="display-a margin-bottom_30rpx color_B7B7B7">
						<image @click="getTime('3')" class="img-280"
							:src="title_time == '3' ? imgUrl+'366.png' : imgUrl+'280.png'"></image>
						<view @click="getTime('3')">开场3秒显示</view>
						<view style="width: 100rpx;"></view>
						<image @click="getTime('0')" class="img-280"
							:src="title_time == '0' ? imgUrl+'366.png' : imgUrl+'280.png'"></image>
						<view @click="getTime('0')">全程显示标题</view>
					</view>
					<view class="margin-bottom_20rpx display-a-js">
						<view class="font-size_32rpx">视频标题</view>
						<view class="text-align_center" @click="addTitle()">
							<image class="img-367" :src="imgUrl+'367.png'"></image>
							<view class="font-size_26rpx">添加</view>
						</view>
					</view>
					<block v-for="(item,index) in titleArr" :key="index">
						<view class="display-a margin-bottom_30rpx">
							<view class="display-a e-input">
								<input type="text" style="width: 500rpx;" maxlength="18" v-model="titleArr[index]"
									placeholder-class="placeholder" placeholder="请输入标题" />
								<view class="color_B7B7B7 margin-left-auto">{{titleArr[index].length}}/18</view>
							</view>
							<image @click="delTitle(index)" class="img-368" :src="imgUrl+'368.png'"></image>
						</view>
					</block>
				</block>
				<block v-else>
					<view class="display-ac-jc">
						<image class="img-365" :src="imgUrl+'365.png'"></image>
						<view class="color_7c7c7c">标题已关闭,可点击上方开启</view>
					</view>
				</block>
			</view>
		</block>

		<block v-if="topType == 2">
			<view class="padding_20rpx_0">
				<scroll-view :scroll-y="true" style="height: 460rpx;">
					<block v-if="modelList.length > 0">
						<view class="display-fw-a">
							<block v-for="(item,index) in modelList" :key="index">
								<view class="flower-list" @click="getModelFor(item)">
									<image class="img-280 img-366"
										:src="modelIds.indexOf(item.id) != -1 ? imgUrl+'366.png' : imgUrl+'280.png'">
									</image>
									<image style="width: 220rpx;" mode="widthFix" :src="item.url"></image>
								</view>

							</block>
						</view>
					</block>
					<block v-else>
						<mescroll-empty></mescroll-empty>
					</block>
				</scroll-view>
			</view>
		</block>

		<block v-if="topType == 3">

			<view class="padding_30rpx">
				<scroll-view :scroll-x="true" style="width: 690rpx; white-space: nowrap;">
					<view class="display-a">
						<block v-for="(item,index) in musicCollectsList" :key="index">
							<view class="music-name" @click="setMusicCollects(item.id_str)"
								:class="item.id_str == mcId ? 'music-name-2' : 'music-name-1'">{{item.name}}</view>
						</block>
					</view>
				</scroll-view>
			</view>
			<view style="padding: 0 30rpx 30rpx;">
				<scroll-view :scroll-y="true" style="height: 600rpx;">
					<block v-for="(item,index) in musicList" :key="index">
						<view class="display-a e-bor">
							<image :key="updateKey" @click="getA(item.isPlay,index,item.play_url.uri)" class="img-301"
								:src="item.isPlay == 2 ? imgUrl+'301.png' : imgUrl+'302.png'"></image>
							<view style="width: 480rpx;">
								<view class="font-size_32rpx margin-bottom_10rpx"
									:class="background_musicId.indexOf(item.id) != -1 ? 'color_90FE8B' : 'color_FFFFFF'">
									{{item.title}}
								</view>
								<view class="display-a color_BFBFBF">
									<view class="font-size_24rpx" style="max-width: 240rpx;">{{item.author}}</view>
									<view class="e-line-music"></view>
									<view class="font-size_26rpx" style="width: 74rpx;">{{item.durations}}</view>
									<block v-if="background_musicId.indexOf(item.id) != -1">
										<view class="e-line-music"></view>
										<view class="display-a" style="width: 90rpx;" @click="openSpeed(item.id)">
											<image class="img-437" :src="imgUrl+'437.png'"></image>
											<view class="font-size_24rpx color_FFFFFF">音量</view>
										</view>
									</block>
								</view>
							</view>
							<image @click="setMusic(item.play_url.uri,item.id)" class="img-281"
								:src="background_musicId.indexOf(item.id) != -1 ? imgUrl+'281.png' : imgUrl+'280.png'">
							</image>
						</view>
					</block>
				</scroll-view>
			</view>
		</block>

		<block v-if="topType == 6">
			<view class="padding_20rpx color_FFFFFF">
				<view class="display-a-js margin-bottom_10rpx">
					<view class="font-size_30rpx">是否识别视频字幕</view>
					<view class="display-a">
						<switch @change="captionsChange" :checked="videoList[swiper.index].isOpen" color="#43FC8B"
							style="transform:scale(0.9)" />

						<image @click="getBatch()" v-if="videoList.length > 0 && videoList[swiper.index].isOpen"
							class="img-436" :src="imgUrl+'436.png'"></image>
						<view @click="getBatch()" v-if="videoList.length > 0 && videoList[swiper.index].isOpen"
							class="font-size_26rpx">开始划重点</view>
					</view>

				</view>

				<block v-if="videoList.length > 0 && videoList[swiper.index].isOpen">
					<scroll-view :scroll-y="true" style="height: 400rpx;">
						<block v-for="(item,index) in videoList[swiper.index].content" :key="index">
							<view class="display-a margin-bottom_10rpx">
								<view>{{item.TimelineIn+'s - '+item.TimelineOut+'s'}}</view>
								<view class="e-tips">
									原文案长度{{videoList[swiper.index].setContent[index].Content.length}},如有修改，请保持文案长度一致
								</view>
								<!-- <image class="img-435" :src="imgUrl+'435.png'"></image> -->
							</view>
							<view class="input-x">
								<input type="text" placeholder="请输入" v-model="item.Content"
									:maxlength="videoList[swiper.index].setContent[index].Content.length"
									:cursor-spacing="50" placeholder-class="placeholder" />
								<view class="font-size_24rpx text-align_right">
									{{item.Content.length+'/'+videoList[swiper.index].setContent[index].Content.length}}
								</view>
							</view>

						</block>
					</scroll-view>
				</block>

			</view>
		</block>

		<block v-if="topType == 4">

			<view class="color_FFFFFF padding_30rpx">
				<view class="display-a-js margin-bottom_30rpx">
					<view class="font-size_30rpx">是否开启智能剪辑</view>
					<switch @change="switchChange" :checked="captions_swich" color="#43FC8B"
						style="transform:scale(0.9)" />
				</view>

				<block v-if="captions_swich">
					<view class="display-a-js margin-bottom_30rpx">
						<view class="font-size_30rpx">生成视频数量</view>
						<picker mode="selector" :range="multipleArr" :range-key="'value'" @change="getMultipleChange">
							<view class="display-a">
								<view>{{multiple}}个</view>
								<image class="img-58" :src="imgUrl+'58.png'"></image>
							</view>
						</picker>
					</view>

					<view class="font-size_30rpx margin-bottom_10rpx">上传多个视频({{videoSize}}/10)</view>
					<view class="font-size_24rpx color_B7B7B7 margin-bottom_20rpx">
						请上传多个和你业务、产品、店铺、企业宣传相关的视频，用于批量去重剪辑，
						<span class="color_FF0000">最低上传4个视频，视频时长不能低于10秒，支持多选上传，无大小限制</span>，上传的视频越多剪辑去重效果越好
					</view>

					<sunui-upvideo1 ref="selectimgandupload2" :alyUrl="upPicUrl2" :progressSize="10"
						:formData="formData" :maxFileNumber="10" btnName="添加视频" @uploaded="videoLoaded2"
						@change="videoChange2" :before-upload="onBeforeUpload"></sunui-upvideo1>

				</block>
				<block v-else>
					<view class="display-ac-jc">
						<image class="img-365" :src="imgUrl+'365.png'"></image>
						<view class="color_7c7c7c">智剪已关闭,可点击上方开启</view>
					</view>
				</block>

			</view>


		</block>

		<block v-if="topType == 5">
			<view class="padding_20rpx_0">
				<scroll-view :scroll-y="true" style="height: 460rpx;">
					<block v-if="backgroundImgList.length > 0">
						<view class="display-fw-a">
							<block v-for="(item,index) in backgroundImgList" :key="index">
								<view class="flower-list" @click="setbackgroundImg(item.id)">
									<image style="width: 220rpx;"
										:class="backgroundImgId == item.id ? 'flower-list-sel' : ''" mode="widthFix"
										:src="item.url"></image>
								</view>

							</block>
						</view>
					</block>
					<block v-else>
						<mescroll-empty></mescroll-empty>
					</block>
				</scroll-view>
			</view>
		</block>

		<view style="height: 170rpx;"></view>

		<view class="bott-pos display-a">
			<view>
				<view class="color_959595 margin-bottom_10rpx">消耗点数</view>
				<view class="display-a">
					<image class="img-369" :src="imgUrl+'369.png'"></image>
					<view class="color_00ff86">{{clipPrice}}</view>
				</view>
			</view>
			<view @click="getList()" class="get-list">剪辑任务</view>
			<view class="display-a-jc e-save" @click="saveClipTask()">
				<image class="img-370" :src="imgUrl+'370.png'"></image>
				<view>生成视频</view>
			</view>
		</view>
		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="pop-top">划重点</view>
				<view style="background-color: #121212;padding: 20rpx 0 20rpx 20rpx" v-if="videoList.length > 0">
					<scroll-view :scroll-y="true" style="height: 600rpx;">
						<view class="font-size_24rpx margin-bottom_20rpx color_FF0000">注: 一句话最多选三个词组(选中相邻字符即为一个词组)
						</view>
						<block v-for="(item,index) in videoList[swiper.index].content" :key="index">
							<view class="display-fw-a margin-bottom_20rpx" v-if="isKey">
								<block v-for="(items,indexs) in videoList[swiper.index].content[index].setKey"
									:key="indexs">
									<view @click="setKey(items,index,indexs,item)" class="square"
										:class="videoList[swiper.index].content[index].keynote_content.indexOf(indexs) != -1 ? 'square-2' : 'square-1'">
										{{items}}
									</view>
								</block>
							</view>
						</block>
					</scroll-view>
				</view>
				<view class="display-a">
					<view class="cancel color_FF0000" @click="closeBatch()">取消</view>
					<view class="cancel color_20FF86" @click="confirmPop()">确认</view>
				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="pop-bg">
					<image @click="closeSpeed()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">音量调节</view>
					<view style="padding: 50rpx 20rpx;" v-if="musicIndex >= 0">
						<view class="display-a-js">
							<view class="font-size_32rpx">音量</view>
							<view>当前音量: {{Number(background_music[musicIndex].volume)}}</view>
						</view>
						<view>
							<slider activeColor="#29F45E" block-size="18" backgroundColor="#E0E0E0" min="1" max="20"
								:value="Number(background_music[musicIndex].volume)" @change="sliderChange" />
						</view>
					</view>
					<view class="c-but" @click="closeSpeed()">确认</view>
				</view>
			</template>

		</sunui-popup>
	</view>
</template>

<script>
	const base64 = require('@/utils/ali-oos/base64.js'); //Base64,hmac,sha1,crypto相关算法
	require('@/utils/ali-oos/hmac.js');
	require('@/utils/ali-oos/sha1.js');
	const Crypto = require('@/utils/ali-oos/crypto.js');
	export default {
		data() {
			return {

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				clipPrice: uni.getStorageSync('tallySetObj').clip_price,

				imgUrl: this.$imgUrl,

				isWhether: true, //判断重复点击

				topIndex: '', //记录上一个播放下标

				isType: '', //6作品列表

				title_swich: true, //标题开关 true开启

				titleArr: [''],

				topType: '1', //1标题 2模板 3音乐 4智剪 5背景 6文案

				title_time: '3', //3 开场3秒  0 全程

				title: '', //标题

				modelList: [], //模板列表
				modelIds: [], //模板ID
				modelImgs: [], //模板透明层

				backgroundImgList: [], //背景图片列表
				backgroundImgId: '', //背景ID

				isCaptions: true, //是否开启字幕 true

				captions_swich: true, //智剪 true开启
				multipleArr: [{
						id: 1,
						value: 1
					},
					{
						id: 2,
						value: 2
					},
					{
						id: 3,
						value: 3
					},
					{
						id: 4,
						value: 4
					},
					{
						id: 5,
						value: 5
					},
					{
						id: 6,
						value: 6
					},
					{
						id: 7,
						value: 7
					},
					{
						id: 8,
						value: 8
					},
					{
						id: 7,
						value: 9
					},
					{
						id: 10,
						value: 10
					}
				],
				multiple: 1,

				videoList: [], //作品列表
				video_id: [],
				videoObj: [], //选中的作品视频

				swiper: {
					margin: "150rpx",
					index: 0,

				},
				isKey: false,
				musicCollectsList: [], //音乐分类
				mcId: '', //

				musicList: [], //音乐列表
				background_music: [], //背景音乐
				background_musicId: [],
				musicIndex: -1,
				updateKey: false,
				voiceAudioContext: null,

				material_url: '', //上传的视频
				videoSize: 0,

				upPicUrl2: '',

				progress: 0, //上传视频进度条

				formData: {
					'key': '',
					'policy': '',
					'OSSAccessKeyId': '',
					'signature': '',
					'success_action_status': '200',
				},

				policyText: {
					"expiration": "2030-01-01T12:00:00.000Z", //设置该Policy的失效时间，超过这个失效时间之后，就没有办法通过这个policy上传文件了
					"conditions": [
						["content-length-range", 0, 104857600] // 设置上传文件的大小限制    209715200
					]
				},
			}
		},



		onLoad() {
			this.getAliyunConfig();
			this.getModelList();
			this.getBackgroundImgList();
			this.getMusicCollects();
		},

		onShow() {
			if (uni.getStorageSync('uid')) {
				// this.getVideoList();
				// 恢复智剪上传的视频数据
				this.restoreUploadedVideos();
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
							// uni.navigateTo({
							// 	url: '/pages/auth/auth?type=1'
							// })
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		onHide() {
			// 页面隐藏时保存智剪上传的视频数据
			this.saveUploadedVideos();
		},

		onUnload() {
			if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
				this.voiceAudioContext.stop();
			}
			// 页面卸载时保存智剪上传的视频数据
			this.saveUploadedVideos();
		},

		methods: {

			// 保存智剪上传的视频数据
			saveUploadedVideos() {
				try {
					const uploadData = {
						material_url: this.material_url,
						videoSize: this.videoSize,
						timestamp: Date.now() // 添加时间戳，可用于数据过期判断
					};

					// 获取组件中的视频列表
					if (this.$refs.selectimgandupload2 && this.$refs.selectimgandupload2.imgLists) {
						uploadData.imgLists = this.$refs.selectimgandupload2.imgLists;
					}

					uni.setStorageSync('edit_uploaded_videos', uploadData);
					console.log('智剪视频数据已保存:', uploadData);
				} catch (error) {
					console.error('保存智剪视频数据失败:', error);
				}
			},

			// 恢复智剪上传的视频数据
			restoreUploadedVideos() {
				try {
					const uploadData = uni.getStorageSync('edit_uploaded_videos');
					if (uploadData) {
						// 检查数据是否过期（24小时）
						const isExpired = Date.now() - uploadData.timestamp > 24 * 60 * 60 * 1000;

						if (!isExpired) {
							this.material_url = uploadData.material_url || '';
							this.videoSize = uploadData.videoSize || 0;

							// 恢复组件中的视频列表
							if (uploadData.imgLists && this.$refs.selectimgandupload2) {
								this.$nextTick(() => {
									this.$refs.selectimgandupload2.setItems(uploadData.imgLists.map(item => item.url));
									// 手动设置imgLists以保持完整的数据结构
									this.$refs.selectimgandupload2.imgLists = uploadData.imgLists;
								});
							}

							console.log('智剪视频数据已恢复:', uploadData);
						} else {
							// 数据过期，清除缓存
							uni.removeStorageSync('edit_uploaded_videos');
							console.log('智剪视频数据已过期，已清除');
						}
					}
				} catch (error) {
					console.error('恢复智剪视频数据失败:', error);
				}
			},

			// 清除保存的视频数据
			clearUploadedVideos() {
				try {
					uni.removeStorageSync('edit_uploaded_videos');
					console.log('智剪视频数据已清除');
				} catch (error) {
					console.error('清除智剪视频数据失败:', error);
				}
			},

			//语调语速
			openSpeed(id) {
				this.musicIndex = this.background_musicId.indexOf(id);
				console.log("当前下标---->", this.musicIndex);
				this.$refs.pop4.show({
					style: 'background-color:#222127;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeSpeed() {
				this.$refs.pop4.close();
			},

			//音量调节
			sliderChange(e) {
				this.background_music[this.musicIndex].volume = e.detail.value;
			},

			confirmPop() {

				for (let i = 0; i < this.videoList[this.swiper.index].content.length; i++) {
					this.videoList[this.swiper.index].content[i].keynote_content.sort((a, b) => a - b);
				}

				for (let k = 0; k < this.videoList[this.swiper.index].content.length; k++) {
					for (let q = 0; q < this.videoList[this.swiper.index].content[k].keynote_content.length; q++) {
						let getIdex = this.videoList[this.swiper.index].content[k].keynote_content; //选中的关键字下标
						let getText = this.videoList[this.swiper.index].content[k].Content; //文案
						this.videoList[this.swiper.index].content[k].selKey = this.processArray(getIdex,
							getText); //相邻组成的词语
						if (this.videoList[this.swiper.index].content[k].selKey.length > 2) {
							this.$sun.toast("第" + (k + 1) + '句话选中的词组超过了2个,请修改!', 'none');
							return;
						}
					}

				}

				this.videoList[this.swiper.index].keynote_content = [];

				for (let j = 0; j < this.videoList[this.swiper.index].content.length; j++) {

					this.videoList[this.swiper.index].finalContent[j].Content = this.videoList[this.swiper.index].content[
						j].Content;

					for (let o = 0; o < this.videoList[this.swiper.index].content[j].selKey.length; o++) {

						let word = this.videoList[this.swiper.index].content[j].selKey[o].word;

						let arrKey = this.videoList[this.swiper.index].finalContent[j].Content.split(word);

						console.log("分割数组----->", arrKey);

						this.videoList[this.swiper.index].finalContent[j].Content = arrKey[0] + "{\\\\fs70\\\\b1}" + word +
							"{\\\\fs\\\\b0}" + arrKey[1];

						console.log("组合结果----->", this.videoList[this.swiper.index].finalContent[j].Content);

						let keyObj = {
							Content: this.videoList[this.swiper.index].content[j].selKey[o].word, //选中的字符
							TimelineIn: this.videoList[this.swiper.index].content[j].TimelineIn,
							TimelineOut: this.videoList[this.swiper.index].content[j].TimelineOut,
						}
						this.videoList[this.swiper.index].keynote_content.push(keyObj);
					}
				}

				console.log("确认输出后====>", this.videoList[this.swiper.index]);
				this.closeBatch();

			},

			processArray(numbers, str) {
				if (!numbers || numbers.length === 0) return [];

				const result = [];
				let temp = [numbers[0]];
				let wordParts = [str[numbers[0]]];

				for (let i = 1; i < numbers.length; i++) {
					if (numbers[i] - numbers[i - 1] === 1) {
						temp.push(numbers[i]);
						wordParts.push(str[numbers[i]]);
					} else {
						result.push({
							numbers: temp.length > 1 ? [...temp] : temp[0],
							word: wordParts.join('')
						});
						temp = [numbers[i]];
						wordParts = [str[numbers[i]]];
					}
				}

				// 处理最后一组
				result.push({
					numbers: temp.length > 1 ? [...temp] : temp[0],
					word: wordParts.join('')
				});

				return result;
			},

			// extractNumbers(arr) {
			// 	if (!arr || arr.length === 0) return [];

			// 	const result = [];
			// 	let temp = [arr[0]];

			// 	for (let i = 1; i < arr.length; i++) {
			// 		if (arr[i] - arr[i - 1] === 1) {
			// 			temp.push(arr[i]);
			// 		} else {
			// 			result.push(temp.length > 1 ? [...temp] : temp[0]);
			// 			temp = [arr[i]];
			// 		}
			// 	}

			// 	// 处理最后一组
			// 	result.push(temp.length > 1 ? [...temp] : temp[0]);

			// 	return result;
			// },

			//选中的关键字
			setKey(value, index, indexs, obj) {
				let getIndex = this.videoList[this.swiper.index].content[index].keynote_content.indexOf(indexs);

				if (getIndex == -1) {

					// let getObj = {
					// 	Content: [],  //选中的字符
					// 	TimelineIn: '4.58',
					// 	TimelineOut: '8.75',
					// }

					this.videoList[this.swiper.index].content[index].keynote_content.push(indexs);
				} else {
					this.videoList[this.swiper.index].content[index].keynote_content.splice(getIndex, 1);
				}

			},

			getBatch() {

				for (let i = 0; i < this.videoList[this.swiper.index].content.length; i++) {
					let videoListObj = this.videoList[this.swiper.index].content[i].Content;
					this.videoList[this.swiper.index].finalContent[i].Content = videoListObj;
					let splitList = videoListObj.split("");
					this.videoList[this.swiper.index].content[i].setKey = splitList;
					if (i == (this.videoList[this.swiper.index].content.length - 1)) {
						this.isKey = true;
					}
				}


				console.log("划重点----->", this.videoList[this.swiper.index]);

				if (this.isKey) {
					this.$refs.pop3.show({
						style: 'background-color:#000;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
						anim: 'bottom',
						position: 'bottom',
						shadeClose: false,
						rgba: 'rgba(50,50,50,.6)'
					});
				}


			},
			closeBatch() {
				this.$refs.pop3.close();
				this.isKey = false;
			},

			//开启字幕
			captionsChange(e) {

				if (this.videoList.length == 0) {
					this.$sun.toast("请先添加视频", 'none');
					return;
				}

				if (this.videoList[this.swiper.index].isOpen) {
					this.videoList[this.swiper.index].isOpen = e.detail.value;
				} else {
					if (this.videoList[this.swiper.index].content.length == 0) {
						this.getSubmitSoundConent();
						this.videoList[this.swiper.index].isOpen = e.detail.value;
					} else {
						this.videoList[this.swiper.index].isOpen = e.detail.value;
					}
				}
			},

			//选中的视频
			otherFun(setObj, ids) {
				this.videoList = setObj;
				this.video_id = ids;
				let clipPrice2 = this.tallySetObj.clip_price;
				if (this.videoList.length == 0) {
					clipPrice2 = clipPrice2 * this.multiple;
				} else {
					clipPrice2 = clipPrice2 * this.multiple * this.videoList.length;
				}
				this.clipPrice = clipPrice2;
			},

			//通过合成视频将声音提交到阿里云
			async getSubmitSoundConent() {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.submitSoundConent,
					data: {
						id: this.videoList[this.swiper.index].id
					}
				});
				if (result.errno == 0) {
					this.getSound(result.data.id);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			async getSound(id) {

				uni.showLoading({
					mask: true,
					title: '请求中...'
				})

				const result = await this.$http.post({
					url: this.$api.getSoundConent,
					data: {
						id: id
					},
					loading: ''
				});
				if (result.errno == -3) {
					setTimeout(() => {
						this.getSound(id);
					}, 1500);
				} else {
					uni.hideLoading();
					if (result.errno == 0) {
						this.$sun.toast("文案获取成功");
						setTimeout(() => {
							this.isWhether = true;
							let getList = [];
							for (let i = 0; i < result.data.content.length; i++) {
								let getObj = {
									Content: result.data.content[i].Content,
									setKey: [], //单个字符数组
									keynote_content: [], //关键字
									selKey: [], //选中的
									TimelineIn: result.data.content[i].TimelineIn,
									TimelineOut: result.data.content[i].TimelineOut,
								}
								getList.push(getObj);
							}
							this.videoList[this.swiper.index].content = getList;
							this.videoList[this.swiper.index].setContent = result.data.content;
							this.videoList[this.swiper.index].finalContent = result.data.content;
						}, 1500);
					} else {
						this.isWhether = true;
						this.$sun.toast(result.message, 'none');
					}
				}
			},

			getSelVideo() {
				uni.navigateTo({
					url: '/pages/edit/selVideo'
				})
			},

			//swiper滑动事件
			swiperChange(e) {
				this.swiper.index = e.detail.current;
			},

			onBeforeUpload(file) {

				if (file.duration < 10) {
					this.$sun.toast("请上传时长大于10秒的视频", 'none');
					return false;
				}

				// 移除大小限制检查，支持无限制上传
				// let maxSize = 100;
				// if (file.size / 1024 / 1024 > maxSize) {
				// 	this.$sun.toast(`视频不能大于${maxSize}M`, 'none');
				// 	return false;
				// }
			},

			videoLoaded2(e) {
				this.material_url = '';
				this.videoSize = e.length;
				for (let i = 0; i < e.length; i++) {
					if ((e.length - 1) == i) {
						this.material_url += e[i].url;
					} else {
						this.material_url += e[i].url + ',';
					}
				}

				// 视频上传完成后自动保存数据
				this.saveUploadedVideos();
			},

			videoChange2() {
				this.$refs.selectimgandupload2.upload();
			},

			/*  阿里云设置  */
			async getAliyunConfig() {
				const result = await this.$http.post({
					url: this.$api.aliyunConfig
				});
				if (result.errno == 0) {
					this.upPicUrl2 = 'https://' + result.data.alioss_domain;
					this.formData.OSSAccessKeyId = result.data.alioss_access_key_id;

					this.formData.policy = base64.encode(JSON.stringify(this.policyText));
					let message = this.formData.policy;
					let bytes = Crypto.HMAC(Crypto.SHA1, message, result.data.alioss_access_key_secret, {
						asBytes: true
					});
					this.formData.signature = Crypto.util.bytesToBase64(bytes);
				}
			},

			//选择倍数
			getMultipleChange(e) {
				this.multiple = this.multipleArr[e.detail.value].value;
				let clipPrice2 = this.tallySetObj.clip_price;
				if (this.video_id.length == 0) {
					clipPrice2 = clipPrice2 * this.multiple;
				} else {
					clipPrice2 = clipPrice2 * this.multiple * this.video_id.length;
				}
				this.clipPrice = clipPrice2;
			},

			//选择背景图
			setbackgroundImg(id) {
				this.backgroundImgId = id;
			},

			//选择模板
			getModelFor(item) {

				let getIndex = this.modelIds.indexOf(item.id);

				if (getIndex != -1) {
					this.modelIds.splice(getIndex, 1);
					this.modelImgs.splice(getIndex, 1);
				} else {
					this.modelIds.push(item.id);
					this.modelImgs.push(item.img_url);
				}

			},

			//删除标题
			delTitle(index) {
				this.titleArr.splice(index, 1);
			},

			//添加标题
			addTitle() {
				this.titleArr.push('');
			},

			//剪辑任务
			getList() {
				// uni.navigateTo({
				// 	url: '/pages/edit/taskList'
				// })
				uni.navigateTo({
					url: '/pages/assets/digital-assets?tabsId=4&type=4'
				})
			},

			//标题开关
			titleSwitchChange(e) {
				this.title_swich = e.detail.value;
			},

			//开启智剪
			switchChange(e) {
				this.captions_swich = e.detail.value;
			},

			//添加剪辑任务
			async saveClipTask() {
				console.log({
					uid: uni.getStorageSync('uid'),
					title: this.titleArr.toString(), //主标题 多个
					// video_id: this.video_id.toString(), //作品ID 多个
					video_id: this.videoList,
					model_id: this.modelIds.toString(), //模板ID 多个
					multiple: this.captions_swich ? this.multiple : '',
					background_music: this.background_music, //背景音乐 多个
					material_url: this.captions_swich ? this.material_url : '',
					background_img_id: this.backgroundImgId,
					title_show_time: this.title_time,
				});
				if (this.video_id.length == 0) {
					this.$sun.toast("请选择需要剪辑的作品", 'none');
					return;
				}

				for (let i = 0; i < this.titleArr.length; i++) {
					if (!this.titleArr[i]) {
						this.$sun.toast("请完善标题", 'none');
						return;
					}
				}

				if (this.modelIds.length == 0) {
					this.$sun.toast("请选择剪辑模板", 'none');
					return;
				}

				if (this.captions_swich && this.videoSize < 4) {
					this.$sun.toast("智能剪辑最低上传4个视频", 'none');
					return;
				}


				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.clipTask,
					data: {
						uid: uni.getStorageSync('uid'),
						title: this.titleArr.toString(), //主标题 多个
						// video_id: this.video_id.toString(), //作品ID 多个
						video_id: this.videoList,
						model_id: this.modelIds.toString(), //模板ID 多个
						multiple: this.captions_swich ? this.multiple : '',
						background_music: this.background_music, //背景音乐 多个
						material_url: this.captions_swich ? this.material_url : '',
						background_img_id: this.backgroundImgId,
						title_show_time: this.title_time,
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					// 任务提交成功后清除保存的视频数据
					this.clearUploadedVideos();
					setTimeout(() => {
						this.getList();
						this.isWhether = true;
					}, 3000);

				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}

			},

			//删除作品
			delVideo(index) {


				this.videoList.splice(index, 1);

				let clipPrice2 = this.tallySetObj.clip_price;
				if (this.videoList.length == 0) {
					clipPrice2 = clipPrice2 * this.multiple;
				} else {
					clipPrice2 = clipPrice2 * this.multiple * this.videoList.length;
				}
				this.clipPrice = clipPrice2;

			},

			//选中作品
			confirmImage(obj) {
				let index = this.video_id.indexOf(obj.id);

				if (index == -1) {
					this.video_id.push(obj.id);
					this.videoObj.push(obj);
				} else {
					this.video_id.splice(index, 1);
					this.videoObj.splice(index, 1);
				}

				let clipPrice2 = this.tallySetObj.clip_price;
				if (this.video_id.length == 0) {
					clipPrice2 = clipPrice2 * this.multiple;
				} else {
					clipPrice2 = clipPrice2 * this.multiple * this.video_id.length;
				}
				this.clipPrice = clipPrice2;

			},

			//创作作品
			getClip() {
				uni.navigateTo({
					url: '/pages/index/clip/clip'
				})
			},

			//播放音频
			getA(isPlay, index, url) {

				this.updateKey = false;

				// uni.showLoading({
				// 	title: '正在试听...',
				// 	// mask: true
				// })
				if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
					this.voiceAudioContext.stop();
					this.voiceAudioContext.destroy();
				}

				this.voiceAudioContext = null;

				this.voiceAudioContext = uni.createInnerAudioContext();

				this.voiceAudioContext.src = url;

				setTimeout(() => {
					if (isPlay == 2) {

						for (let i = 0; i < this.musicList.length; i++) {
							if (this.musicList[i].isPlay == 1) {
								this.musicList[i].isPlay = 2;
								this.updateKey = true;
							}
						}

						this.musicList[index].isPlay = 1;
						this.updateKey = true;
						this.voiceAudioContext.play();
						this.voiceAudioContext.onPlay(() => {

						});
						this.voiceAudioContext.onEnded(() => {
							this.musicList[index].isPlay = 2;
							// uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.$sun.toast("试听完成");
							this.updateKey = true;
						});
						this.voiceAudioContext.onError((err) => {
							// console.log('播放音频出错：', err);
							this.$sun.toast("音频播放出错:" + err, 'none');
							// uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.updateKey = true;
						});
					} else {
						this.musicList[index].isPlay = 2;
						this.updateKey = true;
						// uni.hideLoading();
						this.voiceAudioContext.pause();
						this.voiceAudioContext.onPause(() => {
							// console.log('暂停播放');
						});
					}
				}, 500);
			},

			//选中音乐
			setMusic(url, id) {

				let getIndex = this.background_musicId.indexOf(id);

				if (getIndex != -1) {
					this.background_music.splice(getIndex, 1);
					this.background_musicId.splice(getIndex, 1);
				} else {
					let musicObj = {
						volume: 10,
						url: url
					}
					this.background_music.push(musicObj);
					this.background_musicId.push(id);
				}

				console.log("选中的----->", this.background_music);
			},

			//音乐列表
			async getMusicList() {
				const result = await this.$http.get({
					url: this.$api.musicList,
					data: {
						mcid: this.mcId
					}
				});
				if (result.errno == 0) {
					this.musicList = result.data;
					for (let i = 0; i < this.musicList.length; i++) {
						this.musicList[i].isPlay = 2;
						if (this.musicList[i].duration < 60) {
							this.musicList[i].durations = '00:' + this.musicList[i].duration;
						} else {
							// 将秒数转换为分钟数
							let minutes = Math.floor(this.musicList[i].duration / 60);
							// 将分钟数和秒数格式化为两位数字
							let formattedMinutes = minutes.toString().padStart(2, '0');
							let formattedSeconds = (this.musicList[i].duration % 60).toString().padStart(2, '0');
							this.musicList[i].durations = formattedMinutes + ':' + formattedSeconds;
						}

					}
				}
			},

			//选中分类
			setMusicCollects(id) {
				this.mcId = id;
				this.getMusicList();
			},

			//音乐分类
			async getMusicCollects() {
				const result = await this.$http.get({
					url: this.$api.musicCollects
				});
				if (result.errno == 0) {
					this.musicCollectsList = result.data;
					if (this.musicCollectsList.length > 0) {
						this.mcId = this.musicCollectsList[0].id_str;
						this.getMusicList();
					}

				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},


			//模板列表
			async getModelList() {
				const result = await this.$http.post({
					url: this.$api.modelList
				});
				if (result.errno == 0) {
					this.modelList = result.data.list;
				}
			},

			//背景图片列表
			async getBackgroundImgList() {
				const result = await this.$http.post({
					url: this.$api.backgroundImgList
				});
				if (result.errno == 0) {
					this.backgroundImgList = result.data.list;
					let imgObj = {
						id: '',
						oss_url: this.imgUrl + '371.png',
						url: this.imgUrl + '371.png',
					};
					this.backgroundImgList.unshift(imgObj);
				}
			},

			//选中视频
			voideConfirm() {
				if (this.video_id.length == 0) {
					this.$sun.toast("请选择需要剪辑的视频", 'none');
					return;
				}
				this.closeBatch();
			},

			getBatch(type) {
				this.isType = type;

				// if (type == 2) {

				// 	if (!this.param.id) {
				// 		this.$sun.toast("请选择形象",'none');
				// 		this.isPop = 2;
				// 		return;
				// 	}

				// }

				this.$refs.pop3.show({
					style: 'background-color:#191818;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeBatch() {
				this.$refs.pop3.close();
			},

			getTime(type) {
				this.title_time = type;
			},

			getTopType(type) {
				this.topType = type;

				if (type === 3 && this.musicCollectsList.length === 0) {
					this.getMusicCollects()
				}
			},

		}
	}
</script>

<style lang="scss">
	.c-but {
		width: 690rpx;
		padding: 30rpx 0;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgba(106, 249, 167, 1), rgba(90, 232, 233, 1) 100%);
		margin: 0 0 20rpx;
		font-size: 32rpx;
		color: #FFF;
		text-align: center;
	}

	.p-title {
		font-weight: 600;
		letter-spacing: 4%;
		font-size: 40rpx;
		text-align: center;
		color: #FFF;
		margin-bottom: 30rpx;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.pop-bg {
		position: relative;
		width: 750rpx;
		background-repeat: no-repeat;
		background-size: contain;
		padding: 34rpx 30rpx;
		color: #FFF;
	}

	.img-437 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 6rpx;
	}

	.square-2 {
		background-color: #939393;
		font-size: 30rpx;
		font-weight: 500;
	}

	.square-1 {
		background-color: #2B2B2B;
	}

	.square {
		width: 60rpx;
		height: 60rpx;
		line-height: 60rpx !important;
		text-align: center;
		color: #FFF;
		border-radius: 4rpx;
		margin-right: 12rpx;
		margin-bottom: 14rpx;
	}

	.cancel {
		width: 375rpx;
		text-align: center;
		font-size: 30rpx;
		padding: 40rpx 0;
	}

	.pop-top {
		font-size: 32rpx;
		font-weight: 500;
		color: #ffffFF;
		padding: 32rpx 0;
		text-align: center;
	}

	.img-436 {
		width: 32rpx;
		height: 32rpx;
		margin: 0 10rpx 0 20rpx;
	}

	.e-tips {
		color: #ff0000;
		font-size: 24rpx;
		margin-left: 20rpx;
	}

	.input-x {
		width: 710rpx;
		padding: 20rpx 10rpx;
		background-color: #2B2B2B;
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		input {
			width: 690rpx;
			margin-bottom: 6rpx;
		}
	}

	.img-435 {
		width: 32rpx;
		height: 32rpx;
		margin-left: 20rpx;
	}

	.again-sel {
		font-size: 26rpx;
		width: 150rpx;
		text-align: center;
		padding: 12rpx 0;
		position: fixed;
		right: 0;
		top: 60rpx;
		z-index: 10;
		border-radius: 100rpx 0px 0px 100rpx;
		background: linear-gradient(96.67deg, rgb(45, 255, 155) 7.361%, rgb(28, 205, 120) 99.095%);
	}

	.img-215 {
		width: 36rpx;
		height: 36rpx;
		margin-bottom: 20rpx;
	}

	.top-sel {
		width: 344rpx;
		height: 612rpx;
		border: 1px dashed rgb(105, 248, 170);
		border-radius: 10rpx;
		margin: 0 200rpx 30rpx;
	}

	.model-img {
		width: 344rpx;
		height: 612rpx;
		position: absolute;
		z-index: 6;
		top: 0;
		left: 0;
		border-radius: 10rpx;
	}

	.video-result {
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx;
	}

	.img-346 {
		position: absolute;
		width: 36rpx;
		height: 36rpx;
		top: 40rpx;
		right: 30rpx;
		z-index: 7;
	}

	.img-345 {
		position: absolute;
		width: 36rpx;
		height: 36rpx;
		top: 10rpx;
		right: 10rpx;
		z-index: 7;
	}

	swiper-item {
		width: 344rpx !important;
		height: 612rpx !important;
		margin: 0 20rpx !important;
	}

	.top-swiper {
		// margin-bottom: 30rpx; 

		.bg {
			// padding-top: var(--status-bar-height);
			box-sizing: content-box;
			width: 100%;
			position: relative;

			.placeholder {
				box-sizing: content-box;
				padding-top: 600rpx;
				height: 70rpx;
			}

			.image {
				box-sizing: content-box;
				position: absolute;
				z-index: 1;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				overflow: hidden;

				&::after {
					content: '';
					position: absolute;
					width: 100%;
					height: 100%;
					z-index: 1;
					bottom: 0;
					left: 0;
					height: 65%;
					// background-image: linear-gradient(to bottom ,transparent, #FFF);
				}

				>image {
					position: absolute;
					box-sizing: content-box;
					padding: 60px;
					top: 0;
					left: 0;
					width: 100%;
					height: 80%;
					top: -60px;
					left: -60px;
					filter: blur(50px);
				}
			}
		}

		.box {
			padding-top: var(--status-bar-height);
			box-sizing: content-box;
			position: absolute;
			z-index: 5;
			top: 0;
			left: 0;
			width: 100%;
			height: auto;
		}

		.swiper {
			// width: 344rpx;
			height: 612rpx;
			margin: 0 20rpx;

			.le-img {
				width: 344rpx;
				height: 612rpx;
				display: block;
				transform: scale(0.9);
				transition: transform 0.3s ease-in-out 0s;
				border-radius: 10rpx;

				&.le-active {
					transform: scale(1) !important;
				}
			}

		}
	}

	.img-58 {
		width: 30rpx;
		height: 30rpx;
		margin-left: 8rpx;
		margin-top: 4rpx;
	}

	.flower-list-sel {
		border: 1px solid rgb(0, 238, 124);
	}

	.model-size {
		width: 30rpx;
		height: 30rpx;
		line-height: 30rpx;
		text-align: center;
		border-radius: 50%;
		background-color: #2DFF9B;
		color: #000;
		font-size: 24rpx;
		position: absolute;
		z-index: 5;
		top: -6rpx;
		right: 18rpx;
	}

	.img-366 {
		position: absolute;
		z-index: 3;
		bottom: 14rpx;
		right: 2rpx;
	}

	.color_00ff86 {
		color: #00ff84;
		font-size: 30rpx;
	}

	.img-370 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}

	.get-list {
		width: 186rpx;
		background-color: #373737;
		border-radius: 100rpx;
		padding: 30rpx 0;
		text-align: center;
		color: #FFFfff;
		font-size: 30rpx;
		margin-left: auto;
	}

	.img-369 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 8rpx;
	}

	.img-365 {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 20rpx;
	}

	.bott-pos {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		background-color: #151516;
		padding: 20rpx 20rpx 36rpx;
		z-index: 9;
	}

	.img-367 {
		width: 30rpx;
		height: 30rpx;
		margin-bottom: 4rpx;
	}

	.flower-list {
		position: relative;
		z-index: 1;
		width: 220rpx;
		margin-left: 22rpx;
		margin-bottom: 20rpx;
		// background-color: #313131;
		border-radius: 10rpx;
		// padding: 30rpx 10rpx 0;
	}

	.img-60 {
		width: 40rpx;
		height: 40rpx;
		position: absolute;
		z-index: 9;
		top: 10rpx;
		right: 10rpx;
		border-radius: 50%;
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}

	.e-line-music {
		width: 4rpx;
		height: 24rpx;
		background-color: rgb(191, 191, 191);
		border-radius: 4rpx;
		margin: 0 16rpx;
	}

	.color_BFBFBF {
		color: #BFBFBF;
	}

	.img-281 {
		width: 46rpx;
		height: 46rpx;
		margin-left: auto;
	}

	.e-bor {
		border-bottom: 1px solid rgb(34, 34, 34);
		padding-bottom: 24rpx;
		margin-bottom: 24rpx;
	}

	.img-301 {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
	}

	.music-name-2 {
		color: #FFF;
		font-size: 32rpx;
		font-weight: 600;
	}

	.music-name-1 {
		color: #A1A0A0;
	}

	.music-name {
		font-size: 30rpx;
		margin-right: 40rpx;
	}

	.r-status-2 {
		background-color: #07BD00;
	}

	.r-status-1 {
		background-color: #1377FF;
	}

	.r-status {
		width: 90rpx;
		padding: 6rpx 0;
		text-align: center;
		border-radius: 10rpx 0 10rpx 0;
		position: absolute;
		top: 0;
		left: 0;
		font-size: 24rpx;
		color: #FFF;
		z-index: 2;
	}

	.img-213 {
		position: absolute;
		z-index: 9;
		bottom: 10rpx;
		right: 20rpx;
		width: 50rpx;
		height: 50rpx;
		background-color: #FFF;
		border-radius: 50%;
	}

	.c-img {
		position: relative;
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
		margin-left: 26rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: inline-table;

	}

	.sel-c-img {
		position: relative;
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx;
		margin-right: 22rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: inline-table;

	}

	.decode-img {
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
	}

	.sel-decode-img {
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx;
	}

	.c-top {
		width: 750rpx;
		padding: 30rpx 20rpx;
	}

	.img-279 {
		width: 90rpx;
		height: 90rpx;
		margin-bottom: 10rpx;
	}

	.sel-video {
		width: 400rpx;
		height: 700rpx;
		border: 1px dashed rgb(96, 96, 96);
		border-radius: 10rpx;
		margin: 0 0 30rpx 172rpx;
	}

	.p-but {
		width: 670rpx;
		margin: 40rpx 50rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(89, 238, 82), rgb(176, 255, 130) 100%);
		text-align: center;
		color: #000;
		padding: 26rpx 0;
		font-size: 30rpx;
		font-weight: 600;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.img-280 {
		width: 34rpx;
		height: 34rpx;
		border-radius: 100rpx;
		margin-right: 12rpx;
	}

	.e-input {
		background-color: #282828;
		border-radius: 10rpx;
		width: 620rpx;
		padding: 20rpx;
	}

	.img-368 {
		width: 44rpx;
		height: 44rpx;
		margin-left: auto;
	}

	.color_90FE8B {
		color: #90FE8B;
	}

	.e-line-1 {
		width: 42rpx;
		height: 6rpx;
		border-radius: 4rpx;
		background: #90FE8B;
	}

	.e-line {
		width: 42rpx;
		height: 6rpx;
		border-radius: 4rpx;
		background: #191818;
	}

	.img-283 {
		width: 36rpx;
		height: 36rpx;
	}

	.e-top {
		background: #191818;
		padding: 20rpx 0 0;
	}

	.clip-price {
		margin-left: 10rpx;
		font-size: 24rpx;
	}

	.e-save {
		width: 300rpx;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(106, 249, 167), rgb(90, 232, 233) 100%);
		text-align: center;
		color: #000;
		padding: 30rpx 0;
		margin-left: 20rpx;
		font-size: 30rpx;
	}

	.img-278 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 8rpx;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000 !important;
	}
</style>