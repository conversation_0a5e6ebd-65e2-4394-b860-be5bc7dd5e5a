<template>
	<view>
		<view class="h_20rpx"></view>
		<mescroll-body ref="mescrollRef" :isShowEmptys="true" :height="windowHeight+'rpx'" @init="mescrollInit"
			@down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-if="list.length > 0">
				<view class="display-fw-a" style="padding: 0 10rpx 0 20rpx;">
					<block v-for="(item,index) in list" :key="index">
						<view v-if="item.id">
							<view class="r-frame-w" @click="confirmImage(item)">
								<image class="img-345"
									:src="listId.indexOf(item.id) == -1 ? imgUrl+'89.png' : imgUrl+'346.png'"></image>
								<!-- <view class="r-status r-status-2" v-if="item.current_status == 'success'">已完成</view>
								<view class="r-status r-status-3" v-else-if="item.current_status == 'fail'">已失败
								</view>
								<view class="r-status r-status-1" v-else>生成中</view> -->
								<block v-if="item.current_status == 'success'">
									<block v-if="item.result_cover">
										<image class="r-video-w" mode="widthFix" :src="item.result_cover"></image>
									</block>
									<block v-else>
										<image class="r-video-w" mode="widthFix" :src="item.local_cover"></image>
									</block>
								</block>

							</view>
							<view class="frame-title margin-bottom_20rpx">
								<view class="display-a-js padding-bottom_10rpx margin-bottom_10rpx p-bo2">
									<view style="width: 210rpx;" class="font-size_26rpx font-overflow color_FFFFFF">
										{{item.name}}
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>

			</block>
			<block v-else>
				<view class="display-ac-jc">
					<image class="nodata" src="../../static/nodata.png"></image>
					<view class="nodata-tips">您还没有作品，快去创作一个吧~</view>
					<!-- <view class="nodata-but" @click="getClip()">去创建作品</view> -->
				</view>
			</block>
		</mescroll-body>

		<view style="height: 170rpx;"></view>
		<view class="bott-but">
			<view class="confirm-but" @click="returnNav()" v-if="isClip">上传视频</view>
			<view class="confirm-but" @click="returnNav()">确认选择</view>
		</view>

	</view>
</template>

<script>
	const base64 = require('../../utils/ali-oos/base64.js');
	const Crypto = require('../../utils/ali-oos/crypto.js');
	require('../../utils/ali-oos/hmac.js');
	require('../../utils/ali-oos/sha1.js');

	export default {
		data() {
			return {

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],
				listId: [],
				listObj: [],

				windowHeight: '',

				imgUrl: this.$imgUrl,
				isClip: false,

				// 阿里云上传相关配置
				upPicUrl2: '',
				progress: 0, // 上传视频进度条
				formData: {
					'key': '',
					'policy': '',
					'OSSAccessKeyId': '',
					'signature': '',
					'success_action_status': '200',
				},
				policyText: {
					"expiration": "2030-01-01T12:00:00.000Z", // 设置该Policy的失效时间
					"conditions": [
						["content-length-range", 0, 524288000] // 设置上传文件的大小限制
					]
				},

				// 上传的视频信息
				video_url: '', // 视频路径
				video_cover_url: '' // 视频封面路径


			}
		},

		onLoad(options) {
			if (options.isClip) {
				this.isClip = true
			}
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 194;
				},
			})

			// 初始化阿里云配置
			this.getAliyunConfig();
		},

		onShow() {
			if (uni.getStorageSync('uid')) {

				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});

			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		methods: {
			// 获取阿里云配置
			async getAliyunConfig() {
				const result = await this.$http.post({
					url: this.$api.aliyunConfig
				});
				if (result.errno == 0) {
					this.upPicUrl2 = 'https://' + result.data.alioss_domain;
					this.formData.OSSAccessKeyId = result.data.alioss_access_key_id;

					this.formData.policy = base64.encode(JSON.stringify(this.policyText));
					let message = this.formData.policy;
					let bytes = Crypto.HMAC(Crypto.SHA1, message, result.data.alioss_access_key_secret, {
						asBytes: true
					});
					this.formData.signature = Crypto.util.bytesToBase64(bytes);
				}
			},

			// 视频上传选择
			addVideoImg() {
				// 验证阿里云配置
				if (!this.upPicUrl2) {
					uni.showToast({
						title: '请配置阿里云',
						icon: 'none'
					});
					return;
				}

				uni.chooseVideo({
					count: 1,
					compressed: false,
					sourceType: ['album'],
					success: res => {
						let file = res.tempFilePath;
						let suffix = 'mp4';
						if (res.tempFilePath) {
							suffix = res.tempFilePath.split(".");
						} else {
							this.$sun.toast("视频资源异常,请重新选择!", 'none');
							return;
						}

						// 限制视频大小为100M
						let maxSize = 100;
						if (res.size / 1024 / 1024 > maxSize) {
							this.$sun.toast(`视频不能超过${maxSize}M`, 'none');
							return;
						}

						this.uploadBaseVideo(file, suffix[suffix.length - 1]);
					},
					complete: function() {
						// 选择完成
					},
					fail: function(err) {
						console.log("uni.chooseVideo err---->", err);
					}
				});
			},

			// 上传视频到阿里云
			uploadBaseVideo(file, suffix) {
				this.video_url = '';
				this.video_cover_url = '';

				// 设置一个变量来存储数字
				let count = 0;
				// 设置一个变量来存储定时器
				let timer = null;

				uni.showLoading({
					title: '上传中...' + count + '%',
					mask: true
				});

				// 设置定时器更新数字
				timer = setInterval(() => {
					uni.showLoading({
						title: `上传中... ${count}%`,
					});
				}, 300); // 每0.3秒更新一次

				this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150) + '.' + suffix;

				console.log("this.formData.key---->", this.formData.key);

				// 创建上传对象
				const task = uni.uploadFile({
					url: this.upPicUrl2,
					filePath: file,
					fileType: 'video/mp4',
					name: 'file',
					formData: this.formData,
					header: {},
					success: uploadRes => {
						console.log('uploadRes', uploadRes);

						if (uploadRes.statusCode != 200) {
							uni.showToast({
								title: '上传失败 : ' + uploadRes.data,
								icon: 'none'
							});
							clearInterval(timer);
							uni.hideLoading();
						} else {
							count = 100;
							this.video_url = this.upPicUrl2 + '/' + this.formData.key;
							// 生成视频封面链接
							this.video_cover_url = this.video_url + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto';

							clearInterval(timer);
							uni.hideLoading();

							// 上传成功提示
							uni.showToast({
								title: '视频上传成功',
								icon: 'success'
							});

							console.log('视频链接:', this.video_url);
							console.log('封面链接:', this.video_cover_url);

							// 上传成功后的处理
							this.handleUploadSuccess();
						}
					},
					fail: e => {
						uni.showToast({
							title: '上传失败,' + e,
							icon: 'none'
						});
						clearInterval(timer);
						uni.hideLoading();
					}
				});

				task.onProgressUpdate(res => {
					if (res.progress > 0 && count < 100) {
						count = res.progress;
					}
				});
			},

			// 处理上传成功后的逻辑
			handleUploadSuccess() {
				if (this.isClip) {
					// 如果是剪辑模式，将上传的视频信息存储并跳转
					const uploadedVideoData = {
						id: new Date().getTime(), // 使用时间戳作为临时ID
						result: this.video_url,
						result_cover: this.video_cover_url,
						name: '上传的视频',
						current_status: 'success'
					};

					uni.setStorageSync('clipData', uploadedVideoData);
					uni.navigateTo({
						url: `/subPackages/subPackageA/clip`,
					});
				} else {
					// 如果是选择模式，将视频信息传递给上一页
					const uploadedVideoInfo = {
						video_url: this.video_url,
						video_cover_url: this.video_cover_url
					};

					var pages = getCurrentPages();
					if (pages.length > 1) {
						var prevPage = pages[pages.length - 2]; //上一个页面
						if (prevPage.$vm.handleUploadedVideo) {
							prevPage.$vm.handleUploadedVideo(uploadedVideoInfo);
						}
					}
					uni.navigateBack();
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			//返回上一页
			returnNav() {
				if (this.isClip) {
					// 如果是剪辑模式，检查是否有选中的视频
					if (this.listObj.length <= 0) {
						// 如果没有选中视频，则触发上传视频功能
						this.addVideoImg();
						return;
					}
					uni.setStorageSync('clipData', this.listObj)
					uni.navigateTo({
						url: `/subPackages/subPackageA/clip`,
					});
					return
				}

				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 2]; //上一个页面
				prevPage.$vm.otherFun(this.listObj, this.listId); //重点$vm
				uni.navigateBack();
			},

			//选中作品
			confirmImage(obj) {
				let index = this.listId.indexOf(obj.id);
				let setObj = {}
				if (this.isClip) {
					setObj = {
						id: obj.id,
						result_cover: obj.result_cover,
						result: obj.result
					}
				} else {
					setObj = {
						id: obj.id,
						result_cover: obj.result_cover,
						isOpen: false, //true开启 false关闭
						disabled: true,
						setContent: [], //初始数据
						content: [], //可修改数据
						finalContent: [], //最终文案
						keynote_content: [] //关键字
					};
				}


				if (index == -1) {
					if (this.isClip && this.listId.length === 1) {
						this.$sun.toast('只能选择一个视频', 'none');
						return
					}
					this.listId.push(obj.id);
					this.listObj.push(setObj);
				} else {
					this.listId.splice(index, 1);
					this.listObj.splice(index, 1);
				}
			},

			//作品记录
			async upCallback(scroll) {

				let getUrl = '';
				let getData = {};

				getUrl = this.$api.videoList;
				getData = {
					uid: uni.getStorageSync('uid'),
					current_status: 'success',
					page: scroll.num,
					psize: 15
				}

				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},

		}
	}
</script>

<style lang="scss">
	.img-345 {
		position: absolute;
		width: 36rpx;
		height: 36rpx;
		top: 10rpx;
		right: 10rpx;
		z-index: 7;
	}

	.confirm-but {
		flex: 1;
		text-align: center;
		padding: 28rpx 0;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(106, 249, 167), rgb(90, 232, 233) 100%);
		color: #FFF;
		font-size: 30rpx;
	}

	.bott-but {
		position: fixed;
		display: flex;
		gap: 20rpx;
		bottom: 0;
		width: 750rpx;
		padding: 20rpx 120rpx 40rpx;
		z-index: 15;
	}

	.p-bo2 {
		border-bottom: 1px solid #232323;
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}

	.img-129 {
		// width: 344rpx;
		// height: 612rpx;
		width: 230rpx;
		height: 409rpx;
		position: relative;
		z-index: 1;
		border-radius: 10rpx 10rpx 0 0;
	}

	.img-158 {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		z-index: 2;
		top: 49%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.r-text {
		text-align: center;
		font-size: 26rpx;
		padding-top: 440rpx;
	}

	.img-59 {
		width: 32rpx;
		height: 32rpx;
	}

	.r-img-bg {
		// width: 344rpx;
		// height: 612rpx;
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx 10rpx 0 0;
		background-repeat: no-repeat;
		background-size: contain;
	}

	.r-video-w {
		// width: 344rpx;
		width: 230rpx;
		border-radius: 10rpx 10rpx 0 0;
		position: absolute;
		z-index: 1;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);

	}

	.r-status-3 {
		background-color: #FF0000;
	}

	.r-status-2 {
		background-color: #07BD00;
	}

	.r-status-1 {
		background-color: #1377FF;
	}

	.r-status {
		width: 90rpx;
		padding: 6rpx 0;
		text-align: center;
		border-radius: 10rpx 0 10rpx 0;
		position: absolute;
		top: 0;
		left: 0;
		font-size: 24rpx;
		color: #FFF;
		z-index: 2;
	}

	.frame-title {
		background-color: #414141;
		padding: 10rpx 10rpx 0;
		// width: 344rpx;
		width: 230rpx;
		border-radius: 0 0 10rpx 10rpx;
		// margin-bottom: 20rpx;
	}

	.r-frame-w {
		// width: 344rpx;
		width: 230rpx;
		border-radius: 10rpx 10rpx 0 0;
		margin-right: 10rpx;
		// margin-bottom: 20rpx;
		// background-color: #414141;
		position: relative;
		// height: 612rpx;
		height: 409rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: block;
		/* 避免底部空白 */
	}

	page {
		width: 100%;
		overflow-x: hidden;
		background-color: #000;
		border-top: 1px solid rgb(56, 56, 56);
	}
</style>