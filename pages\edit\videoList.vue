<template>
	<view>
		<view class="h_20rpx"></view>
		<!-- 上传视频按钮 -->
		<view class="upload-video-btn" @click="showUploadModal">
			<view class="upload-btn-content">
				<image class="upload-icon" :src="imgUrl + '226.png'"></image>
				<text class="upload-text">上传视频</text>
			</view>
		</view>
		<mescroll-body ref="mescrollRef" :isShowEmptys="true" :height="windowHeight+'rpx'" @init="mescrollInit"
			@down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-if="list.length > 0">
				<block v-for="(item,index) in list" :key="index">
					<view class="display list-public">
						<view class="frame">
							<image @click="getVideoOpen(item)" class="r-video" mode="widthFix"
								:src="item.complete_cover"></image>
						</view>
						<view style="position: relative;">
							<view class="color_FFFFFF font-size_32rpx font-weight_bold margin-bottom_20rpx">
								{{item.title}}
							</view>
							<view class="color_A1A1A1 margin-bottom_20rpx">{{item.create_time}}</view>
							<view class="display-a" style="position: absolute;bottom: 0;z-index: 9;" v-if="type == 1">
								<view class="v-forward" v-if="repostSetIsOpen == 1" @click="getWorks(item)">转发</view>
								<view class="v-forward margin-left_30rpx" v-if="repostSetIsOpen == 1"
									style="width: 160rpx;" @click="getSee(item)">查看数据</view>
								<view class="v-forward margin-left_30rpx" @click="longpress(item)">下载</view>
							</view>

						</view>
					</view>
				</block>
			</block>
			<block v-else>
				<view class="display-ac-jc">
					<image class="nodata" src="../../static/nodata.png"></image>
					<view class="nodata-tips">暂无数据~</view>
					<!-- <view class="nodata-but">去创建剪辑任务</view> -->
				</view>
			</block>
		</mescroll-body>

		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="close-works" @click="closeWorks()">关闭</view>
				<view class="works-pop">请选择发布的渠道</view>
				<view class="display-a padding_40rpx_0">
					<view class="width_374rpx-center" @click="getCode(1)">
						<image class="img-181" :src="imgUrl + '181.png'"></image>
						<view class="color_FFFFFF">发布到D音</view>
					</view>
					<view class="width_374rpx-center" @click="getCode(2)">
						<image class="img-181" :src="imgUrl + '182.png'"></image>
						<view class="color_FFFFFF">发布到K手</view>
					</view>
				</view>
				<view style="height: 60rpx;"></view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="display-ac-jc padding_30rpx_0">
					<image class="img-183" :src="imgUrl + '183.png'"></image>
					<view class="color_FFFFFF">
						打开{{codeWay == 1 ? 'D音' : 'K手'}}扫一扫下方二维码,转发到{{codeWay == 1 ? 'D音' : 'K手'}}发布</view>
					<image class="pop-qrcode" :src="codeWay == 1 ? obj.dy_qrcode : obj.ks_qrcode"></image>
					<view class="color_FFFFFF">{{obj.name}}</view>
					<view class="pop-save" @click="imageLongpress()">保存到相册</view>
				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="rescuePop">
			<template v-slot:content>
				<view style="width: 540rpx;height: 960rpx;border-radius: 10rpx;">
					<video style="width: 540rpx;height: 960rpx;border-radius: 10rpx;" :poster="obj.complete_cover"
						:src="obj.complete_video"></video>
				</view>
			</template>
		</sunui-popup>

		<!-- 上传视频弹窗 -->
		<sunui-popup ref="uploadPop">
			<template v-slot:content>
				<view class="upload-modal">
					<view class="upload-header">
						<view class="upload-title">上传视频</view>
						<view class="upload-close" @click="closeUploadModal">×</view>
					</view>

					<view class="upload-content">
						<!-- 标题输入区域 -->
						<view class="upload-section">
							<view class="upload-label">
								<text>视频标题</text>
								<text class="required">*</text>
							</view>
							<view class="title-input-area">
								<input class="title-input" v-model="uploadTitle" placeholder="请输入视频标题（20字以内）"
									maxlength="20" :focus="false" />
								<view class="title-count">{{uploadTitle.length}}/20</view>
							</view>
						</view>

						<!-- 视频上传区域 -->
						<view class="upload-section">
							<view class="upload-label">
								<text>上传视频</text>
								<text class="required">*</text>
							</view>

							<!-- 已上传的视频列表 -->
							<scroll-view class="uploaded-videos-container" scroll-y="true" v-if="uploadQueue.length > 0">
								<view class="uploaded-videos-list">
									<view class="uploaded-video-item" v-for="(video, index) in uploadQueue" :key="index">
										<view class="video-preview-small">
											<video class="preview-video-small" :src="video.uploadUrl" v-if="video.uploaded && video.uploadUrl"></video>
											<view class="video-placeholder-small" v-else>
												<image class="upload-icon-small" :src="imgUrl+'226.png'"></image>
												<view class="upload-status">
													<text v-if="!video.uploaded && currentUploadIndex === index">上传中...</text>
													<text v-else-if="!video.uploaded">等待上传</text>
													<text v-else>上传完成</text>
												</view>
											</view>
										</view>
										<view class="video-info">
											<text class="video-size">{{formatFileSize(video.size)}}</text>
											<text class="video-duration" v-if="video.duration">{{formatDuration(video.duration)}}s</text>
										</view>
									</view>
								</view>
							</scroll-view>

							<!-- 上传按钮区域 -->
							<view class="video-upload-area-small" @click="chooseVideo">
								<view class="upload-placeholder-small">
									<image class="upload-icon-medium" :src="imgUrl+'226.png'"></image>
									<view class="upload-tip-small">{{uploadQueue.length > 0 ? '继续添加视频' : '点击上传视频'}}</view>
									<view class="upload-desc-small">支持MP4/MOV格式，可多选上传，无大小限制</view>
								</view>
							</view>
						</view>

						<!-- 封面上传区域 - 暂时隐藏 -->
						<!-- <view class="upload-section">
							<view class="upload-label">
								<text>上传封面</text>
								<text class="optional">（可选）</text>
							</view>
							<view class="cover-upload-area" @click="chooseCover">
								<view class="cover-preview" v-if="uploadCoverUrl">
									<image class="preview-cover" :src="uploadCoverUrl" mode="aspectFill"></image>
								</view>
								<view class="upload-placeholder-small" v-else>
									<image class="upload-icon-small" :src="imgUrl+'226.png'"></image>
									<view class="upload-tip-small">点击上传封面</view>
								</view>
							</view>
						</view> -->
					</view>

					<view class="upload-footer">
						<view class="upload-btn-cancel" @click="closeUploadModal">取消</view>
						<view class="upload-btn-submit" @click="submitUpload"
							:class="{disabled: !uploadTitle.trim() || !hasUploadedVideos || isUploading}">
							{{isUploading ? '上传中...' : '确认上传'}}
						</view>
					</view>
				</view>
			</template>
		</sunui-popup>

	</view>
</template>

<script>
	const base64 = require('@/utils/ali-oos/base64.js'); //Base64,hmac,sha1,crypto相关算法
	require('@/utils/ali-oos/hmac.js');
	require('@/utils/ali-oos/sha1.js');
	const Crypto = require('@/utils/ali-oos/crypto.js');
	export default {
		data() {
			return {

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

				windowHeight: '',

				imgUrl: this.$imgUrl,

				id: '',

				type: '', // 1作品进入 2矩阵进入

				obj: {}, //

				codeWay: '', //1抖音 2快手

				repostSetIsOpen: '', //转发设置 1开启

				isWhether: true, //判断重复点击

				// 上传相关变量
				uploadTitle: '', // 上传的视频标题
				uploadVideoUrl: '', // 上传的视频URL
				uploadCoverUrl: '', // 上传的封面URL
				isUploading: false, // 上传状态控制
				uploadProgress: 0, // 上传进度
				uploadQueue: [], // 上传队列
				currentUploadIndex: 0, // 当前上传的文件索引
				totalUploadCount: 0, // 总上传文件数
				formData: { // 阿里云上传表单数据
					'key': '',
					'policy': '',
					'OSSAccessKeyId': '',
					'signature': '',
					'success_action_status': '200',
				},
				policyText: { // 阿里云上传策略
					"expiration": "2030-01-01T12:00:00.000Z",
					"conditions": [
						["content-length-range", 0, 524288000]
					]
				},
				upPicUrl2: '', // 阿里云上传地址

			}
		},

		computed: {
			// 检查是否有已上传的视频
			hasUploadedVideos() {
				return this.uploadQueue.some(video => video.uploaded && video.uploadUrl);
			}
		},

		onLoad(options) {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 20;
				},
			})
			if (options.id) {
				this.id = options.id;
			}
			if (options.type) {
				this.type = options.type;
			}
			if (options.name) {
				this.$sun.title(options.name);
			}
			this.getRepostSet();
			this.getAliyunConfig();
		},

		onShow() {
			if (uni.getStorageSync('uid')) {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
							// uni.navigateTo({
							// 	url: '/pages/auth/auth?type=1'
							// })
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		methods: {

			//显示上传弹窗
			showUploadModal() {
				this.$refs.uploadPop.show({
					style: 'background-color:#313131;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},

			//关闭上传弹窗
			closeUploadModal() {
				this.$refs.uploadPop.close();
				// 重置上传状态
				this.uploadTitle = '';
				this.uploadVideoUrl = '';
				this.uploadCoverUrl = '';
				this.isUploading = false;
				this.uploadProgress = 0;
				this.uploadQueue = [];
				this.currentUploadIndex = 0;
				this.totalUploadCount = 0;
			},

			//选择视频
			chooseVideo() {
				if (this.isUploading) {
					return;
				}

				// 验证后端配置
				if (!this.upPicUrl2) {
					uni.showToast({
						title: '请配置阿里云',
						icon: 'none'
					});
					return;
				}

				uni.chooseMedia({
					count: 9, // 支持多选，最多9个视频
					mediaType: ['video'], // 只选择视频
					sourceType: ['album'], // 从相册选择
					maxDuration: 300, // 最长5分钟
					success: res => {
						console.log('选择的视频文件:', res.tempFiles);

						if (!res.tempFiles || res.tempFiles.length === 0) {
							this.$sun.toast("视频资源异常,请重新选择!", 'none');
							return;
						}

						// 处理多个视频文件
						this.uploadMultipleVideos(res.tempFiles);
					},
					fail: function(err) {
						console.log("uni.chooseMedia err---->", err);
						// 如果chooseMedia不支持，回退到chooseVideo
						if (err.errMsg && err.errMsg.includes('chooseMedia:fail')) {
							this.chooseVideoFallback();
						}
					}
				});
			},

			// 回退到uni.chooseVideo的方法
			chooseVideoFallback() {
				uni.chooseVideo({
					count: 1,
					compressed: false,
					sourceType: ['album'],
					success: res => {
						let file = res.tempFilePath;
						let suffix = 'mp4';
						if (res.tempFilePath) {
							suffix = res.tempFilePath.split(".");
						} else {
							this.$sun.toast("视频资源异常,请重新选择!", 'none');
							return;
						}

						// 注意：这里移除了大小限制检查
						this.uploadBaseVideo(file, suffix[suffix.length - 1]);
					},
					fail: function(err) {
						console.log("uni.chooseVideo err---->", err);
					}
				});
			},

			// 处理多个视频文件上传
			uploadMultipleVideos(tempFiles) {
				if (!tempFiles || tempFiles.length === 0) {
					return;
				}

				// 将新选择的视频添加到现有队列中（而不是替换）
				const newVideos = tempFiles.map(file => {
					let suffix = 'mp4';
					if (file.tempFilePath) {
						const pathParts = file.tempFilePath.split(".");
						suffix = pathParts[pathParts.length - 1];
					}
					return {
						tempFilePath: file.tempFilePath,
						suffix: suffix,
						size: file.size,
						duration: file.duration,
						uploaded: false,
						uploadUrl: ''
					};
				});

				// 追加到现有队列
				this.uploadQueue = this.uploadQueue.concat(newVideos);
				this.totalUploadCount = this.uploadQueue.length;

				// 显示选择的视频信息
				const existingCount = this.uploadQueue.length - newVideos.length;
				if (existingCount > 0) {
					this.$sun.toast(`新增${newVideos.length}个视频，总共${this.totalUploadCount}个`, 'success');
				} else {
					this.$sun.toast(`已选择${this.totalUploadCount}个视频文件`, 'success');
				}

				// 开始上传新添加的视频（从当前未上传的视频开始）
				this.startUploadFromCurrent();
			},

			// 从当前位置开始上传
			startUploadFromCurrent() {
				// 找到第一个未上传的视频的索引
				this.currentUploadIndex = this.uploadQueue.findIndex(video => !video.uploaded);

				if (this.currentUploadIndex === -1) {
					// 所有视频都已上传
					this.$sun.toast('所有视频都已上传完成', 'success');
					return;
				}

				// 开始上传
				this.uploadNextVideo();
			},

			// 上传队列中的下一个视频
			uploadNextVideo() {
				if (this.currentUploadIndex >= this.uploadQueue.length) {
					// 所有视频上传完成
					this.onAllVideosUploaded();
					return;
				}

				const currentFile = this.uploadQueue[this.currentUploadIndex];
				if (currentFile.uploaded) {
					this.currentUploadIndex++;
					this.uploadNextVideo();
					return;
				}

				this.uploadBaseVideo(currentFile.tempFilePath, currentFile.suffix, this.currentUploadIndex);
			},

			// 所有视频上传完成的处理
			onAllVideosUploaded() {
				const uploadedVideos = this.uploadQueue.filter(file => file.uploaded);
				this.$sun.toast(`成功上传${uploadedVideos.length}个视频`, 'success');

				// 如果有上传成功的视频，使用第一个作为当前显示的视频
				if (uploadedVideos.length > 0) {
					this.uploadVideoUrl = uploadedVideos[0].uploadUrl;
				}

				this.isUploading = false;
				this.uploadProgress = 0;
				// 不重置currentUploadIndex，保持当前位置
			},

			//上传视频到阿里云
			uploadBaseVideo(file, suffix, queueIndex = -1) {
				if (queueIndex === -1) {
					// 单个文件上传（兼容原有逻辑）
					this.uploadVideoUrl = '';
				}
				this.isUploading = true;

				// 设置进度显示
				let count = 0;
				let timer = null;

				const isQueueUpload = queueIndex >= 0;
				const progressTitle = isQueueUpload
					? `上传中(${queueIndex + 1}/${this.totalUploadCount})...${count}%`
					: `上传中...${count}%`;

				uni.showLoading({
					title: progressTitle,
					mask: true
				});

				// 设置定时器更新进度
				timer = setInterval(() => {
					const newTitle = isQueueUpload
						? `上传中(${queueIndex + 1}/${this.totalUploadCount})...${count}%`
						: `上传中...${count}%`;
					uni.showLoading({
						title: newTitle,
					});
				}, 300);

				this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150) + '.' + suffix;

				// 创建上传对象
				const task = uni.uploadFile({
					url: this.upPicUrl2,
					filePath: file,
					fileType: 'video/mp4',
					name: 'file',
					formData: this.formData,
					header: {},
					success: uploadRes => {
						console.log('uploadRes', uploadRes);

						if (uploadRes.statusCode != 200) {
							uni.showToast({
								title: '上传失败 : ' + uploadRes.data,
								icon: 'none'
							});
							clearInterval(timer);
							uni.hideLoading();

							if (isQueueUpload) {
								// 队列上传失败，继续下一个
								this.currentUploadIndex++;
								this.uploadNextVideo();
							} else {
								this.isUploading = false;
							}
						} else {
							count = 100;
							const uploadUrl = this.upPicUrl2 + '/' + this.formData.key;

							if (isQueueUpload) {
								// 标记当前文件上传成功
								this.uploadQueue[queueIndex].uploaded = true;
								this.uploadQueue[queueIndex].uploadUrl = uploadUrl;

								clearInterval(timer);
								uni.hideLoading();

								// 继续上传下一个文件
								this.currentUploadIndex++;
								this.uploadNextVideo();
							} else {
								// 单文件上传成功
								this.uploadVideoUrl = uploadUrl;
								clearInterval(timer);
								uni.hideLoading();
								this.isUploading = false;
								this.$sun.toast("视频上传成功", 'success');
							}
						}
					},
					fail: e => {
						uni.showToast({
							title: '上传失败,' + e,
							icon: 'none'
						});
						clearInterval(timer);
						uni.hideLoading();

						if (isQueueUpload) {
							// 队列上传失败，继续下一个
							this.currentUploadIndex++;
							this.uploadNextVideo();
						} else {
							this.isUploading = false;
						}
					}
				});
			},

			//选择封面图片
			chooseCover() {
				if (this.isUploading) {
					return;
				}

				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.uploadCover(res.tempFilePaths[0]);
					},
					fail: (err) => {
						console.log('选择封面图片失败:', err);
						this.$sun.toast('选择封面图片失败', 'none');
					}
				});
			},

			//上传封面图片
			uploadCover(filePath) {
				uni.showLoading({
					title: '上传封面中...'
				});

				uni.uploadFile({
					url: this.$api.upload,
					filePath: filePath,
					name: 'file',
					formData: {
						uid: uni.getStorageSync('uid')
					},
					success: (res) => {
						try {
							const data = JSON.parse(res.data);
							console.log(data);
							if (data.errno === 0) {
								this.uploadCoverUrl = data.data;
								uni.hideLoading();
								this.$sun.toast('封面上传成功', 'success');
							} else {
								uni.hideLoading();
								this.$sun.toast(data.message || '封面上传失败', 'none');
							}
						} catch (e) {
							uni.hideLoading();
							this.$sun.toast('解析响应失败', 'none');
						}
					},
					fail: (err) => {
						uni.hideLoading();
						this.$sun.toast('封面上传失败', 'none');
						console.log('封面上传失败:', err);
					}
				});
			},

			//提交上传数据
			async submitUpload() {
				if (!this.uploadTitle.trim()) {
					this.$sun.toast("请输入视频标题", 'error');
					return;
				}

				if (this.uploadTitle.length > 20) {
					this.$sun.toast("标题不能超过20字", 'error');
					return;
				}

				// 检查是否有已上传的视频
				const uploadedVideos = this.uploadQueue.filter(video => video.uploaded && video.uploadUrl);
				if (uploadedVideos.length === 0) {
					this.$sun.toast("请先上传视频", 'error');
					return;
				}

				if (this.isUploading) {
					return;
				}

				this.isUploading = true;

				try {
					uni.showLoading({
						title: '提交中...',
						mask: true
					});

					// 将所有已上传的视频链接组成数组
					const videoUrls = uploadedVideos.map(video => video.uploadUrl);

					// 一次性提交所有视频链接数组
					const result = await this.$http.post({
						url: this.$api.uploadVideoAdd,
						data: {
							uid: uni.getStorageSync('uid'),
							title: this.uploadTitle.trim(),
							cover: this.uploadCoverUrl || '',
							url: videoUrls, // 改为数组格式
							mission_id: this.id
						}
					});

					uni.hideLoading();

					if (result.errno == 0) {
						this.$sun.toast(`成功提交${uploadedVideos.length}个视频`, 'success');
						this.closeUploadModal();
						this.refreshVideoList();
					} else {
						this.$sun.toast(result.message || '提交失败', 'none');
					}
				} catch (error) {
					uni.hideLoading();
					this.$sun.toast('网络错误，请重试', 'none');
					console.error('上传视频API调用失败:', error);
				} finally {
					this.isUploading = false;
				}
			},

			//刷新视频列表
			refreshVideoList() {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},

			// 格式化文件大小
			formatFileSize(bytes) {
				if (!bytes) return '0B';
				const k = 1024;
				const sizes = ['B', 'KB', 'MB', 'GB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
			},

			// 格式化时长
			formatDuration(seconds) {
				if (!seconds) return '0';
				return Math.round(seconds);
			},

			//mescroll初始化
			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},

			//下拉刷新
			downCallback(mescroll) {
				mescroll.resetUpScroll();
			},

			//保存视频到相册
			async longpress(obj) {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				if (obj.complete_video) {

					uni.showLoading({
						mask: true
					})

					uni.downloadFile({
						url: obj.complete_video,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.saveVideoToPhotosAlbum({
									filePath: res.tempFilePath,
									success: (r) => {
										this.isWhether = true;
										uni.hideLoading();
										this.$sun.toast("保存相册成功");
									},
									fail: (e) => {
										// console.log(res);
										if (e.errMsg ==
											'saveVideoToPhotosAlbum:fail auth deny') {
											uni.hideLoading();
											uni.showModal({
												title: '您需要授权相册权限',
												success(res) {
													if (res.confirm) {
														uni.openSetting({
															success(res) {

															},
															fail(res) {
																// console.log(res);
															}
														});
													}
												}
											});
										} else if (e.errMsg ==
											'saveVideoToPhotosAlbum:fail file not exists') {
											this.isWhether = true;
											this.$sun.toast("保存失败,文件不存在!", 'none');
										} else {
											this.isWhether = true;
											console.log("err==>", e);
											this.$sun.toast("保存失败", 'error');
										}
									}
								});
							} else {
								this.isWhether = true;
								this.$sun.toast("下载失败", 'error');
							}
						},
						fail: err => {
							this.$sun.toast(err, 'error');
						}
					});
				} else {
					this.isWhether = true;
					this.$sun.toast("请检查阿里云地址视频是否已删除!", 'none');
				}
			},

			//视频弹窗
			getVideoOpen(item) {
				// this.obj = item;
				// this.$refs.rescuePop.show({
				// 	style: 'width:540rpx; height:960rpx;background-color: #FFFFFF;border-radius: 5px;',
				// 	anim: 'center',
				// 	shadeClose: false, //使用户不能点击其它关闭页面
				// 	bottomClose: true
				// });
				//高级 快速版
				let param = {
					base_video: item.complete_video,
					id: item.id,
					name: item.title
				};

				uni.navigateTo({
					url: '/pages/index/videos?type=2&param=' + encodeURIComponent(JSON.stringify(param))
				})
			},

			imageLongpress() {

				let getWayUrl = this.codeWay == 1 ? this.obj.dy_qrcode : this.obj.ks_qrcode;

				uni.showLoading({
					mask: true
				})

				uni.getImageInfo({
					src: getWayUrl,
					success: (image) => {
						uni.saveImageToPhotosAlbum({
							filePath: image.path,
							success: (res) => {
								uni.hideLoading();
								this.$sun.toast("保存相册成功");
							},
							fail: (res) => {
								uni.hideLoading();
								// this.$sun.toast(res, 'error');
								if (res.errMsg == 'saveImageToPhotosAlbum:fail auth deny') {
									uni.showModal({
										title: '您需要授权相册权限',
										success(res) {
											if (res.confirm) {
												uni.openSetting({
													success(res) {

													},
													fail(res) {
														console.log(res);
													}
												});
											}
										}
									});
								}
							}
						});
					}
				});
			},

			//转发设置
			async getRepostSet() {
				const result = await this.$http.post({
					url: this.$api.repostSet
				});
				if (result.errno == 0) {
					this.repostSetIsOpen = result.data.is_open;
				}
			},

			//获取阿里云配置
			async getAliyunConfig() {
				const result = await this.$http.post({
					url: this.$api.aliyunConfig
				});
				if (result.errno == 0) {
					this.upPicUrl2 = 'https://' + result.data.alioss_domain;
					this.formData.OSSAccessKeyId = result.data.alioss_access_key_id;

					this.formData.policy = base64.encode(JSON.stringify(this.policyText));
					let message = this.formData.policy;
					let bytes = Crypto.HMAC(Crypto.SHA1, message, result.data.alioss_access_key_secret, {
						asBytes: true
					});
					this.formData.signature = Crypto.util.bytesToBase64(bytes);
				}
			},

			getSee(obj) {
				uni.navigateTo({
					url: '/pages/works/see?id=' + obj.id
				})
			},

			//二维码弹窗
			getOpenCode() {
				this.$refs.pop4.show({
					style: 'width:610rpx;background-color: #313131;border-radius: 5px;',
					anim: 'center',
					shadeClose: false, //使用户不能点击其它关闭页面
					bottomClose: true
				});
			},

			getCode(way) {
				this.codeWay = way;
				//抖音码
				if (way == 1) {
					if (this.obj.dy_qrcode) {
						this.closeWorks();
						this.getOpenCode();
					} else {
						this.getSaveCode(way);
					}
				}
				//快手码
				if (way == 2) {
					if (this.obj.ks_qrcode) {
						this.closeWorks();
						this.getOpenCode();
					} else {
						this.getSaveCode(way);
					}
				}
			},

			//生成抖音快手码
			async getSaveCode(way) {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				// this.loadingTips = '正在生成中';
				// this.loadingOpen();
				uni.showLoading({
					title: '正在生成中',
					mask: true
				})

				const result = await this.$http.post({
					url: this.$api.clipVideoExhibit,
					data: {
						uid: uni.getStorageSync('uid'),
						way: way,
						video_id: this.obj.id,
						type: this.type
					}
				});
				if (result.errno == 0) {
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
					if (way == 1) {
						this.obj.dy_qrcode = result.data;
					}
					if (way == 2) {
						this.obj.ks_qrcode = result.data;
					}
					// this.loadingClose();
					uni.hideLoading();
					setTimeout(() => {
						this.isWhether = true;
						this.closeWorks();
						this.getOpenCode();

					}, 1000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}

			},

			//发布作品弹窗
			getWorks(obj) {
				this.obj = obj;
				this.$refs.pop3.show({
					style: 'background-color:#313131;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			closeWorks() {
				this.$refs.pop3.close();
			},

			async upCallback(scroll) {

				let getUrl = '';
				let getData = {};

				getUrl = this.$api.clipVideoList;
				getData = {
					uid: uni.getStorageSync('uid'),
					id: this.id,
					page: scroll.num,
					psize: 12
				}

				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

		}
	}
</script>

<style lang="scss">
	/* 上传视频按钮样式 */
	.upload-video-btn {
		margin: 20rpx;
		background: linear-gradient(90deg, #4088FF, #64F2FB);
		border-radius: 10rpx;
		padding: 20rpx;
	}

	.upload-btn-content {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.upload-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.upload-text {
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: bold;
	}

	/* 上传弹窗样式 */
	.upload-modal {
		width: 750rpx;
		background-color: #313131;
		border-radius: 10rpx 10rpx 0 0;
		padding: 0;
	}

	.upload-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #444;
	}

	.upload-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
	}

	.upload-close {
		color: #999;
		font-size: 60rpx;
		line-height: 1;
	}

	.upload-content {
		padding: 30rpx;
	}

	.upload-section {
		margin-bottom: 40rpx;
	}

	.upload-label {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		color: #FFFFFF;
		font-size: 32rpx;
	}

	.required {
		color: #FF0000;
		margin-left: 10rpx;
	}

	.optional {
		color: #999;
		margin-left: 10rpx;
		font-size: 28rpx;
	}

	/* 标题输入框样式 */
	.title-input-area {
		position: relative;
		width: 100%;
	}

	.title-input {
		width: 100%;
		height: 80rpx;
		background-color: #222;
		border: 2rpx solid #666;
		border-radius: 10rpx;
		padding: 0 30rpx;
		color: #FFFFFF;
		font-size: 32rpx;
		box-sizing: border-box;
	}

	.title-input::placeholder {
		color: #999;
	}

	.title-count {
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #999;
		font-size: 24rpx;
		pointer-events: none;
	}

	.video-upload-area {
		width: 100%;
		height: 300rpx;
		border: 2rpx dashed #666;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #222;
	}

	.video-preview {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.preview-video {
		width: 100%;
		height: 100%;
	}

	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	.upload-icon-large {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
	}

	.upload-tip {
		color: #64F2FB;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.upload-desc {
		color: #999;
		font-size: 24rpx;
	}

	.cover-upload-area {
		width: 200rpx;
		height: 200rpx;
		border: 2rpx dashed #666;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #222;
	}

	.cover-preview {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.preview-cover {
		width: 100%;
		height: 100%;
	}

	.upload-placeholder-small {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	.upload-icon-small {
		width: 50rpx; /* 调整图标大小 */
		height: 50rpx;
		margin-bottom: 8rpx;
	}

	.upload-tip-small {
		color: #64F2FB;
		font-size: 24rpx;
	}

	.upload-footer {
		display: flex;
		justify-content: space-between;
		padding: 30rpx;
		border-top: 1rpx solid #444;
	}

	.upload-btn-cancel {
		width: 300rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background-color: #666;
		color: #FFFFFF;
		border-radius: 10rpx;
		font-size: 32rpx;
	}

	.upload-btn-submit {
		width: 300rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: linear-gradient(90deg, #4088FF, #64F2FB);
		color: #FFFFFF;
		border-radius: 10rpx;
		font-size: 32rpx;
	}

	.upload-btn-submit.disabled {
		background: #666;
		color: #999;
	}

	/* 已上传视频列表容器样式 */
	.uploaded-videos-container {
		height: 300rpx; /* 固定高度，可滚动 */
		margin-bottom: 20rpx;
	}

	.uploaded-videos-list {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
		padding: 10rpx;
	}

	.uploaded-video-item {
		width: 187rpx; /* 缩小为原来的2/3：280 * 2/3 ≈ 187 */
		background-color: #222;
		border-radius: 8rpx;
		padding: 10rpx;
	}

	.video-preview-small {
		width: 100%;
		height: 331rpx; /* 按9:16比例计算：187 * 16 / 9 ≈ 331 */
		border-radius: 6rpx;
		overflow: hidden;
		margin-bottom: 10rpx;
	}

	.preview-video-small {
		width: 100%;
		height: 100%;
	}

	.video-placeholder-small {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #333;
		border-radius: 6rpx;
	}

	.upload-status {
		color: #64F2FB;
		font-size: 20rpx; /* 调整字体大小 */
		margin-top: 5rpx;
		text-align: center;
	}

	.video-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 3rpx; /* 调整间距 */
	}

	.video-size {
		color: #999;
		font-size: 20rpx; /* 调整字体大小 */
	}

	.video-duration {
		color: #64F2FB;
		font-size: 20rpx; /* 调整字体大小 */
	}

	/* 小尺寸上传按钮样式 */
	.video-upload-area-small {
		width: 100%;
		height: 150rpx; /* 减小高度 */
		border: 2rpx dashed #666;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #222;
	}

	.upload-placeholder-small {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	.upload-icon-medium {
		width: 50rpx; /* 中等大小图标 */
		height: 50rpx;
		margin-bottom: 10rpx;
	}

	.upload-tip-small {
		color: #64F2FB;
		font-size: 28rpx; /* 稍小的字体 */
		margin-bottom: 5rpx;
	}

	.upload-desc-small {
		color: #999;
		font-size: 22rpx; /* 更小的描述文字 */
	}

	.pop-save {
		width: 400rpx;
		text-align: center;
		font-size: 30rpx;
		background-color: #4088FF;
		color: #FFF;
		padding: 20rpx;
		border-radius: 10rpx;
		margin-top: 80rpx;
	}

	.pop-qrcode {
		width: 320rpx;
		height: 320rpx;
		margin: 50rpx 0;
	}

	.img-183 {
		width: 284rpx;
		height: 84rpx;
		margin-bottom: 14rpx;
	}

	.works-pop {
		font-size: 32rpx;
		color: #FFF;
		text-align: center;
		padding: 0 0 60rpx;
	}

	.close-works {
		color: #FFF;
		text-align: right;
		padding: 14rpx 30rpx;
	}

	.img-181 {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		margin-bottom: 10rpx;
	}

	.v-forward {
		width: 120rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(89, 238, 82), rgb(130, 255, 242) 100%);
		color: #000;
		font-size: 30rpx;
		padding: 16rpx 20rpx;
	}

	.list-public {
		background-color: #000;
		padding: 0;
		margin: 0 20rpx 30rpx;
	}

	.r-video {
		// width: 344rpx;
		width: 230rpx;
		border-radius: 10rpx;
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.frame {
		position: relative;
		width: 230rpx;
		// height: 612rpx;
		height: 409rpx;
		border-radius: 10rpx;
		margin-right: 20rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: block;
		/* 避免底部空白 */
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
</style>