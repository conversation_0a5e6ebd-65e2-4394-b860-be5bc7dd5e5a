<template>
	<view v-if="uid">

		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">

			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					批量视频创作
				</view>
			</view>

			<block v-if="step == 1">
				<view class="display-a padding_0_20rpx margin-bottom_30rpx">
					<view class="line-batc"></view>
					<view class="font-size_32rpx color_FFFFFF">形象列表</view>
					<view class="display-a margin-left-auto" v-if="paramId.length > 0" @click="getBatch()">
						<view class="color_3AFA9A font-size_26rpx">更多形象</view>
						<image class="img-417" :src="imgUrl+'417.png'"></image>
					</view>
				</view>
				<block v-if="paramId.length > 0">
					<scroll-view :scroll-x="true" style="width: 730rpx;height: 434rpx;">
						<view class="display-a">
							<block v-for="(item,index) in param" :key="index">
								<view class="c-img" style="margin-bottom: 0;margin-left: 20rpx;">
									<image class="img-60" @click="delImg(index)" :src="imgUrl+'60.png'">
									</image>
									<image class="decode-img" mode="widthFix" :src="item.decode_img"></image>
								</view>
							</block>
						</view>
					</scroll-view>
				</block>
				<view class="add-img display-ac-jc" @click="getBatch()" v-else>
					<image class="img-215 margin-bottom_20rpx" :src="imgUrl+'215.png'"></image>
					<view class="color_3AFA9A font-size_26rpx">选择形象(最多选9个)</view>
				</view>
				<view class="display-a padding_0_20rpx margin-bottom_30rpx">
					<view class="line-batc"></view>
					<view class="font-size_32rpx color_FFFFFF">选择音色</view>
				</view>
				<view class="display-a padding_0_20rpx margin-bottom_20rpx">
					<view v-if="cloneSet.xunfei_sound_clone_swich == 1" @click="getVoiceType(3)" class="batc-font"
						:class="voiceType == 3 ? 'batc-font-1' : 'batc-font-2'">专业版</view>
					<view v-if="cloneSet.voice_high_open == 1" @click="getVoiceType(2)" class="batc-font"
						:class="voiceType == 2 ? 'batc-font-1' : 'batc-font-2'">高保真</view>
				</view>
				<scroll-view :scroll-y="true" :style="{'height': ''+windowHeight+'rpx'}">
					<block v-if="voicesList.length > 0">
						<block v-for="(item,index) in voicesList" :key="index">
							<view class="list-public display-a">
								<view class="display-a-jc r-play" :key="updateKey"
									@click="playAudio(item.isPlay,index,item.voice_urls[0])">
									<image class="img-94" :key="updateKey"
										:src="item.isPlay == 1 ? imgUrl+'95.png' : imgUrl+'94.png'">
									</image>
								</view>

								<view @click="getSel(item)" style="width: 460rpx;">
									<view class="color_FFFFFF font-size_32rpx font-weight_bold margin-bottom_10rpx">
										{{item.name}}
									</view>
									<view class="color_A1A1A1 font-size_26rpx">{{item.create_time}}</view>
								</view>

								<image @click="getSel(item)" class="img-240 margin-left-auto"
									:src="soundObj.id == item.id ? imgUrl+'241.png' : imgUrl+'240.png'"></image>
							</view>
						</block>
					</block>
					<block v-else>
						<view class="display-ac-jc">
							<image class="nodata" :src="imgUrl+'418.png'"></image>
							<view class="nodata-tips">您还没有音色，快去复刻一个吧~</view>
							<view class="nodata-but" @click="getSenior()">立即复刻音色</view>
						</view>
					</block>
				</scroll-view>
			</block>
			<block v-if="step == 2">
				<view class="color_C3C2C2 padding_0_20rpx margin-bottom_20rpx">
					已选<span class="font-weight_bold color_FF0000 margin_0_4rpx">{{paramId.length}}</span>个数字人形象,
					<span class="font-weight_bold color_FF0000 margin_0_4rpx">{{msgTextList.length}}</span>个文案,
					将生成<span class="font-weight_bold color_FF0000 margin_0_4rpx">{{videoTotal}}</span>个视频
				</view>
				<view class="step-frame">
					<view class="color_FFFFFF margin-bottom_10rpx">视频标题</view>
					<input type="text" class="input-x" maxlength="16" v-model="name" placeholder-class="placeholder"
						placeholder="请输入视频标题" />
				</view>
				<block v-for="(item,index) in msgTextList" :key="index">
					<view class="step-frame">
						<view class="display-a-js margin-bottom_20rpx">
							<view class="line-top-name-sel"><span class="font-size_28rpx">文案{{index+1}}</span></view>
							<image class="img-60-text" @click="delText(index)" :src="imgUrl+'60.png'"></image>
						</view>

						<view style="width: 678rpx;" class="font-overflow5 color_FFFFFF margin-bottom_20rpx">{{item}}
						</view>
						<view class="display-a" @click="editMsg(index)">
							<view class="margin-left-auto color_3AFA9A font-weight_bold">去编辑</view>
							<image class="img-417" :src="imgUrl+'417.png'"></image>
						</view>
					</view>
				</block>
				<view class="add-text display-a-jc" @click="previousStep(4)">
					<image class="img-417 margin-right_16rpx" :src="imgUrl+'215.png'"></image>
					<view>添加文案</view>
				</view>
			</block>
			<block v-if="step == 3">
				<image class="img-416" :src="imgUrl+'416.png'"></image>
				<view class="look-work" @click="getLook()">查看作品</view>
				<view class="color_FFFFFF text-align_center font-size_30rpx" @click="getIndeNex()">返回首页</view>
			</block>
			<block v-if="step == 4 || step == 5">
				<view class="step-frame">
					<view class="color_FFFFFF margin-bottom_10rpx">文案</view>
					<view class="frame-top">
						<textarea v-model="msgText" maxlength="520" :cursor-spacing="50"
							:placeholder="voiceType == 2 ? '高保真默认加了<speak></speak>标签,所以默认有15个字' : '我是虚拟数字人,请输入您的配音文案'"
							placeholder-class="placeholder"></textarea>
						<view class="display-a margin-bottom_20rpx">
							<view class="msg-tips" v-if="voiceType == 2">{{msgText.length+15}}/500字</view>
							<view class="msg-tips" v-if="voiceType == 3">{{msgText.length}}/500字</view>
						</view>
						<view class="display-a" style="color: #EBEAEA;">
							<view class="display-a margin-right_40rpx" @click="getAccountInfo()">
								<image class="img-409" :src="imgUrl+'410.png'"></image>
								<view class="font-size_26rpx">一键仿写</view>
							</view>
							<!-- <view class="display-a">
								<image class="img-409" :src="imgUrl+'411.png'"></image>
								<view class="font-size_26rpx">AI翻译</view>
							</view> -->
							<view class="clear-clip" @click="getClear()">清空</view>
						</view>
					</view>
				</view>
				<!-- <view class="step-frame">
					<view class="color_FFFFFF margin-bottom_10rpx">生成文案条数(选填)</view>
					<input type="number" class="input-x" maxlength="1" v-model="msgNumber" placeholder-class="placeholder" placeholder="请输入需生成的文案条数(最大值9)" />
				</view> -->
			</block>

			<view style="height: 160rpx;"></view>
		</view>

		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="color_FFFFFF padding_30rpx_0">
					<view @click="closeBatch()" class="get-close">x</view>
					<view class="p-title">请选择数字人形象</view>
					<view class="display-a line-top" v-if="setLineList.length > 1">
						<block v-for="(item,index) in setLineList" :key="index">
							<view class="display-ac-jc width_186rpx-center" @click="getIsSel(item.id)">
								<view :class="isSel == item.id ? 'line-top-name-sel' : 'color_A0A0A1'">{{item.name}}
								</view>
								<view class="line-top-bot" :class="isSel == item.id ? 'line-top-bot-sel' : ''"></view>
							</view>
						</block>
					</view>
					<scroll-view :scroll-y="true" :style="{'height': avatarList.length > 3 ? '860rpx;' : '430rpx;'}">
						<block v-if="avatarList.length > 0">
							<view class="display-fw-a">
								<block v-for="(item,index) in avatarList" :key="index">
									<view class="c-img" @click="confirmImage(item)" :id="'id'+item.id">
										<image class="img-213"
											:src="paramId.indexOf(item.id) != -1 ? imgUrl+'213.png' : imgUrl+'212.png'">
										</image>
										<image class="decode-img" mode="widthFix" :src="item.video_cover"></image>
									</view>
								</block>
							</view>
						</block>
						<block v-else>
							<view>
								<image @click="getClone()" class="img-211" :src="imgUrl+'211.png'"></image>
							</view>
						</block>
					</scroll-view>
					<view v-if="avatarList.length > 0" class="p-but" @click="closeBatch()">确定</view>
				</view>
			</template>
		</sunui-popup>

		<view class="pos-bott">
			<view v-if="step == 1" @click="nextStep(2)" class="but-next">下一步</view>
			<view v-if="step == 2" class="display-a-js">
				<view class="previous-step" @click="previousStep(1)">上一步</view>
				<view class="but-next" style="width: 430rpx;" @click="getSynthesis()">提交
					<!-- :(
					<block v-if="isSel == 3">{{tallySetObj.composite_deduct}}点/分</block>
					<block v-if="isSel == 2">{{tallySetObj.woni_video_deduct}}点/秒</block>
					<block v-if="isSel == 1">{{tallySetObj.video_deduct}}点/秒</block>
					<block v-if="isSel == 4">{{tallySetObj.composite_deduct_four}}点/分</block>
					) -->
				</view>
			</view>
			<view v-if="step == 4" class="display-a-js">
				<view class="previous-step" @click="previousStep(2)">返回</view>
				<view class="but-next" @click="msgConfirm(1)" style="width: 430rpx;">确认</view>
			</view>
			<view v-if="step == 5" class="display-a-js">
				<view class="previous-step" @click="previousStep(2)">返回</view>
				<view class="but-next" @click="msgConfirm(2)" style="width: 430rpx;">确认编辑</view>
			</view>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {

				uid: '',

				imgUrl: this.$imgUrl,

				heightSystemss: '',
				statusBarHeightss: '',
				windowHeight: '',

				step: '1', //步骤 4添加文案 5编辑文案

				isSel: '', //
				paramId: [],

				param: [
					// {
					// 	decode_img: '',
					// 	id: '',
					// 	isSel: '',
					// 	width: '',
					// 	height: '',
					// }
				],

				avatarList: [],

				voiceType: '', //2 高保真 3专业
				voicesList: [], //音色列表

				voiceAudioContext: null,
				soundObj: {
					name: '',
					url: '', //
					id: '', //
				},
				updateKey: false,

				name: '', //视频标题

				msgTextList: [], //文案列表
				msgText: '', //文案
				msgNumber: '', //文案条数
				editIndex: '', //编辑文案下标

				soundId: [], //音频ID列表

				tallyTotal: 0, //扣点总数
				videoTotal: 0, //合成视频数

				templateObj: {}, //模板设置

				setLineList: '', //已开启的线路
				cloneSet: uni.getStorageSync("cloneSet"), //克隆开关设置
				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				isWhether: true, //判断重复点击

			}
		},

		onLoad() {
			if (this.cloneSet.xunfei_sound_clone_swich == 1) {
				this.voiceType = 3;
			} else if (this.cloneSet.voice_high_open == 1) {
				this.voiceType = 2;
			}
			this.getTemplate();
			this.getSystemInfo();
		},

		onShow() {
			if (uni.getStorageSync('uid')) {
				this.uid = uni.getStorageSync('uid');
				this.getIndexWay();
				this.getVoice2();
			}
		},

		onUnload() {

			if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
				this.voiceAudioContext.stop();
			}

		},

		methods: {

			//模板设置
			async getTemplate() {
				const result = await this.$http.post({
					url: this.$api.template,
				});
				if (result.errno == 0) {
					this.templateObj = result.data;
				}
			},

			//线路自定义名称
			async getIndexWay() {
				const result = await this.$http.post({
					url: this.$api.indexWay,
					data: {
						type: 1
					}
				});
				if (result.errno == 0) {
					this.setLineList = result.data;
					if (!this.isSel) {
						this.isSel = this.setLineList[0].id;
					}
					this.getAvatarList();
				}
			},

			//提交任务
			getSynthesis() {

				if (this.paramId.length == 0) {
					this.$sun.toast("请先选择形象", 'none');
					return;
				}

				if (!this.soundObj.id) {
					this.$sun.toast("请先选择音色", 'none');
					return;
				}

				if (!this.name) {
					this.$sun.toast("请先输入视频标题", 'none');
					return;
				}

				if (this.msgTextList.length == 0) {
					this.$sun.toast("请先添加文案", 'none');
					return;
				}

				uni.getSetting({
					withSubscriptions: true,
					success: (res) => {
						console.log(res.subscriptionsSetting);
						if (res.subscriptionsSetting.mainSwitch == false) {
							this.getSave();
						} else {
							// 获取下发权限
							uni.requestSubscribeMessage({
								tmplIds: [this.templateObj
									.generate_task_template
								], //此处写在后台获取的模板ID，可以写多个模板ID，看自己的需求
								success: (data) => {
									if (data[this.templateObj.generate_task_template] ==
										'accept') { //accept--用户同意 reject--用户拒绝 ban--微信后台封禁,可不管
										this.getSave();
									} else {
										uni.showModal({
											title: '温馨提示',
											content: '您已拒绝授权，将无法在微信中收到通知！',
											showCancel: false,
											success: res => {
												if (res.confirm) {
													// 这里可以写自己的逻辑
													this.getSave();
													console.log('拒绝授权', data);
												}
											}
										})
									}
								},
								fail: (err) => {

									this.getSave();
									console.log('失败', err);
								},
								complete: (result) => {

									console.log('完成', result);
								}
							});
						}
					}
				});


			},

			async getSave() {

				let isFlag = '';

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				uni.showLoading({
					mask: true,
					title: '任务上传中'
				})

				for (let i = 0; i < this.msgTextList.length; i++) {
					let getUrl = '';
					let getDate = '';

					if (this.voiceType == 2) { // voiceType 1 入门 2高保真 3专业
						getUrl = this.$api.ttsSsml;
						getDate = {
							uid: uni.getStorageSync('uid'),
							voice_id: this.soundObj.id,
							text: '<speak>' + this.msgTextList[i] + '</speak>'
						}
					}

					if (this.voiceType == 3) { // voiceType 1 入门 2高保真 3专业
						getUrl = this.$api.voiceClone;
						getDate = {
							uid: uni.getStorageSync('uid'),
							voice_id: this.soundObj.id,
							msg: this.msgTextList[i]
						}
					}

					const result = await this.$http.post({
						url: getUrl,
						data: getDate,
						loading: ''
					});
					if (result.errno == 0) {

						if (result.data.id) {

							if (this.isSel == 2 || this.isSel == 3) {
								if (i == (this.msgTextList.length - 1)) {
									console.log("循环完成23", this.soundId);
									isFlag = 2;
								}
								this.getSpeedSoundAdd(result.data.url, isFlag);
							} else {
								this.soundId.push(result.data.id);
								if (i == (this.msgTextList.length - 1)) {
									console.log("循环完成14", this.soundId);
									isFlag = 1;
								}
							}


						} else {
							// this.$sun.toast("音频生成失败,请联系平台处理!", 'none');
							this.isWhether = true;
						}

					} else {
						// this.$sun.toast(result.message, 'none');
						this.isWhether = true;
					}
				}

				if (isFlag == 1) {
					console.log("循环已完成，进行下一步", this.soundId);
					this.getGenerateVideo();
					uni.hideLoading();
				}

			},

			//极速视频添加声音 线路三/线路二
			async getSpeedSoundAdd(aUrl, isFlag) {

				uni.showLoading({
					mask: true,
					title: '任务上传中'
				})

				const result = await this.$http.post({
					url: this.$api.speedSoundAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.name, //视频标题	必传
						url: aUrl, // 音频链接 从/video/sendTts接口返回	必传
					},
					loading: ''
				});
				if (result.errno == 0) {
					this.soundId.push(result.data);
					if (isFlag == 2) {
						this.getGenerateVideo();
						uni.hideLoading();
					}
				} else {
					uni.hideLoading();
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//批量合成视频
			async getGenerateVideo() {

				uni.showLoading({
					mask: true,
					title: '任务上传中'
				})

				const result = await this.$http.post({
					url: this.$api.generateVideo,
					data: {
						uid: uni.getStorageSync('uid'),
						sound_id: this.soundId,
						avatar_id: this.param,
						type: this.isSel == 1 ? 1 : this.isSel == 2 ? 3 : this.isSel == 3 ? 2 : 4,
						name: this.name
					}
				});
				if (result.errno == 0) {
					uni.hideLoading();
					this.$sun.toast("任务提交成功");
					setTimeout(() => {
						this.soundId = [];
						this.step = 3;
						this.isWhether = true;
					}, 2000);
				} else {
					this.soundId = [];
					uni.hideLoading();
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
			},

			//计算总点数
			totalNumber() {
				this.videoTotal = this.paramId.length * this.msgTextList.length; //合成视频数

				// <!-- <block v-if="param.isSel == 3">{{tallySetObj.composite_deduct}}点/分</block>
				// <block v-if="param.isSel == 2">{{tallySetObj.woni_video_deduct}}点/秒</block>
				// <block v-if="param.isSel == 1">{{tallySetObj.video_deduct}}点/秒</block>
				// <block v-if="param.isSel == 4">{{tallySetObj.composite_deduct_four}}点/分</block> -->
				//xunfei_sound_generate 专业版
				//high_fidelity_sound 高保真

			},

			//删除文案
			delText(index) {
				this.msgTextList.splice(index, 1);
				this.totalNumber();
			},

			//编辑文案
			editMsg(index) {
				this.step = 5;
				this.editIndex = index;
				this.msgText = this.msgTextList[index];
			},

			//添加文案
			msgConfirm(type) {
				if (!this.msgText) {
					this.$sun.toast("请先输入文案", 'none');
					return;
				}

				if (this.msgText.length > 500) {
					this.$sun.toast("请把文案限制在500字以内", 'none');
					return;
				}

				if (type == 1) {
					this.msgTextList.push(this.msgText);
				}
				if (type == 2) {
					this.msgTextList[this.editIndex] = this.msgText;
				}

				this.step = 2;
				this.msgText = '';
				this.totalNumber();
			},

			//查询点数是否足够
			async getAccountInfo() {

				if (!this.msgText) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}

				// uni.showModal({
				// 	content: this.tallySetObj.ai_create_deduct + '点/1次,一键仿写会覆盖当前文案,是否确认?',
				// 	cancelText: "取消",
				// 	confirmText: "确认",
				// 	success: async (res) => {
				// 		if (res.confirm) {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.accountInfo,
					data: {
						type: 1,
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.getCopyImitation();
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
				// 			} else if (res.cancel) {

				// 			}
				// 		}
				// 	})

			},

			//一键仿写
			async getCopyImitation() {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: this.msgText,
						countType: 3,
						ipStatus: 2,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					// this.$sun.toast("操作成功");
					// setTimeout(() => {
					// 	this.msgText = result.data;
					// 	this.isWhether = true;
					// }, 1000);
					this.getAICreation(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(answer) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: "一键仿写",
						type: 17,
						question: this.msgText, //拼接的文本
						answer: answer, // 接口返回的文本
						words: answer.length
					}
				});
				if (result.errno == 0) {
					this.$sun.toast("操作成功");
					setTimeout(() => {
						this.msgText = answer;
						this.isWhether = true;
					}, 1000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//清空文本
			getClear() {
				this.msgText = '';
			},

			//返回首页
			getIndeNex() {
				uni.reLaunch({
					url: '/pages/index/index'
				})
			},

			//查看作品
			getLook() {
				uni.redirectTo({
					url: '/pages/assets/digital-assets?tabsId=2&type=1&tabsNextId=' + this.isSel
				})
			},

			//上一步
			previousStep(type) {
				if (type == 4) {
					if (this.msgTextList.length == 9) {
						this.$sun.toast("最多添加9个文案", 'error');
						return;
					}
				}
				this.step = type;
			},

			//播放音频
			async playAudio(isPlay, index, url) {

				this.updateKey = false;

				if (url) {

					if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
						this.voiceAudioContext.stop();
						this.voiceAudioContext.destroy();
					}


					this.voiceAudioContext = null;

					this.voiceAudioContext = uni.createInnerAudioContext();

					this.voiceAudioContext.src = url;
					setTimeout(() => {
						if (isPlay == 2) {
							for (let i = 0; i < this.voicesList.length; i++) {
								if (this.voicesList[i].isPlay == 1) {
									this.voicesList[i].isPlay = 2;
									this.updateKey = true;
								}
							}
							this.voicesList[index].isPlay = 1;
							this.updateKey = true;
							this.voiceAudioContext.play();
							this.voiceAudioContext.onPlay(() => {
								// this.isWhether = true;
								// this.$forceUpdate();
								console.log('开始播放');
							});
							this.voiceAudioContext.onEnded(() => {
								this.voicesList[index].isPlay = 2;
								this.updateKey = true;
								// uni.hideLoading();
								this.voiceAudioContext.destroy();
								this.voiceAudioContext = null;
								this.$sun.toast("试听完成");
								// this.isWhether = true;

							});
							this.voiceAudioContext.onError((err) => {
								this.voicesList[index].isPlay = 2;
								this.updateKey = true;
								this.$sun.toast("音频播放出错:" + err, 'none');
								// uni.hideLoading();
								this.voiceAudioContext.destroy();
								this.voiceAudioContext = null;
								// this.isWhether = true;
							});
						} else {
							this.voicesList[index].isPlay = 2;
							this.updateKey = true;
							// uni.hideLoading();
							this.voiceAudioContext.pause();
							this.voiceAudioContext.onPause(() => {
								console.log('暂停播放');
							});
						}
					}, 500);
				}

			},

			//选中音频
			getSel(obj) {
				this.soundObj = {
					name: obj.name,
					url: obj.voice_urls[0], //
					id: obj.id, //
				}
			},

			getVoiceType(type) {
				this.voiceType = type;
				this.getVoice2();
			},

			//高保真克隆记录
			async getVoice2() {
				const result = await this.$http.post({
					url: this.$api.voiceTrainList,
					data: {
						uid: uni.getStorageSync('uid'),
						current_status: 'completed',
						train_mode: this.voiceType, //1-入门 2-高保真 3专业
						buy_expire: 2, //资产市场购买 2未过期
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {
					this.voicesList = result.data.list;
					for (let i = 0; i < this.voicesList.length; i++) {
						this.voicesList[i].isPlay = 2;
					}
				}
			},

			//高保真声音克隆
			getSenior() {
				if (this.voiceType == 3) {
					uni.navigateTo({
						url: '/pages/index/voice/highFidelity'
					})
				}
				if (this.voiceType == 2) {
					uni.navigateTo({
						url: '/pages/index/voice/senior'
					})
				}
			},

			getBatch() {
				if (uni.getStorageSync('uid')) {
					this.$refs.pop3.show({
						style: 'background-color:#111317;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
						anim: 'bottom',
						position: 'bottom',
						shadeClose: false,
						rgba: 'rgba(50,50,50,.6)'
					});
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "返回",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {

							}
						}
					})
				}
			},
			closeBatch() {
				this.$refs.pop3.close();
			},

			//前往克隆页
			getClone() {
				let compressed = 1;
				if (this.isSel == 4) {
					compressed = 2;
				}
				uni.navigateTo({
					url: '/pages/index/clone/clone?compressed=' + compressed
				})
			},

			//下一步
			nextStep(type) {

				if (this.paramId.length == 0) {
					this.$sun.toast("请先选择形象", 'none');
					return;
				}

				if (!this.soundObj.id) {
					this.$sun.toast("请先选择音色", 'none');
					return;
				}

				this.step = type;

				this.totalNumber();
			},

			//删除形象
			delImg(index) {
				this.paramId.splice(index, 1);
				this.param.splice(index, 1);
			},

			//确认形象
			confirmImage(obj) {

				if (this.paramId.length >= 9) {
					this.$sun.toast("最多选9个形象", 'none');
					return;
				}

				let getIndex = this.paramId.indexOf(obj.id);

				if (getIndex == -1) {
					this.paramId.push(obj.id);
					let paramObj = {
						decode_img: obj.video_cover,
						id: obj.id,
					}
					if (this.isSel == 3 || this.isSel == 2) {
						paramObj = {
							decode_img: obj.video_cover,
							id: obj.id,
							width: '',
							height: '',
						}
						this.getImage(paramObj);
					} else {
						this.param.push(paramObj);
					}
				} else {
					this.paramId.splice(getIndex, 1);
					this.param.splice(getIndex, 1);
				}

			},

			//获取图片信息
			getImage(obj) {

				uni.getImageInfo({
					src: obj.decode_img,
					success: (image) => {

						uni.showLoading({
							mask: true,
							title: '加载中...'
						})

						console.log("图片信息---->", image.width, image.height);

						obj.width = image.width;
						obj.height = image.height;

						this.param.push(obj);

						uni.hideLoading();

						console.log("获取的------>", this.param);

					},
					fail: err => {
						uni.hideLoading();
						this.$sun.toast(err, 'none');
						console.log(err);
					}
				});
			},

			getIsSel(type) {
				if (this.paramId.length > 0) {
					uni.showModal({
						content: "切换线路会清空当前已选形象,是否确认切换",
						cancelText: "取消",
						confirmText: "确认",
						success: (res) => {
							if (res.confirm) {
								this.isSel = type;
								this.paramId = [];
								this.param = [];
								this.getAvatarList();
							} else if (res.cancel) {

							}
						}
					})
				} else {
					this.isSel = type;
					this.paramId = [];
					this.param = [];
					this.getAvatarList();
				}
			},

			//我的形象
			async getAvatarList() {

				const result = await this.$http.post({
					url: this.$api.avatarList,
					data: {
						uid: uni.getStorageSync('uid'),
						buy_expire: 2, //资产市场购买 2未过期
						current_status: this.isSel == 1 ? 'completed' : '',
						new_current_status: this.isSel == 2 ? 'completed' : '',
						composite_current_status: this.isSel == 3 ? 'completed' : '',
						four_current_status: this.isSel == 4 ? 'completed' : '',
						definition: this.isSel == 4 ? 2 : 1,
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {
					this.avatarList = result.data.list;
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						this.windowHeight = res.windowHeight * 2 - 890;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.img-60-text {
		width: 34rpx;
		height: 34rpx;
	}

	.input-x {
		width: 638rpx;
		padding: 20rpx;
		border-radius: 10rpx;
		background-color: #232323;
		color: #FFF;
	}

	.placeholder {
		color: #848484;
	}

	.clear-clip {
		width: 90rpx;
		text-align: center;
		background-color: #2D2E33;
		border-radius: 10rpx;
		margin-left: auto;
		color: #EBEAEA;
		font-size: 24rpx;
		padding: 8rpx 0;
	}

	.img-409 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}

	textarea {
		width: 650rpx;
		margin-bottom: 20rpx;
		color: #FFF;
		height: 200rpx;
	}

	.msg-tips {
		// text-align: right;
		// padding-right: 20rpx;
		// padding-bottom: 20rpx;
		// margin-bottom: 20rpx;
		margin-left: auto;
		font-size: 24rpx;
		color: #C9CACA;
	}

	.frame-top {
		width: 678rpx;
		background-color: #232323;
		padding: 20rpx;
		border-radius: 10rpx;
	}

	.step-frame {
		width: 710rpx;
		background-color: #1A1A1A;
		border-radius: 10rpx;
		padding: 26rpx 16rpx 30rpx;
		margin: 0 20rpx 24rpx;
	}

	.look-work {
		width: 460rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(89, 238, 82), rgb(130, 255, 242) 100%);
		padding: 30rpx 0;
		color: #000;
		font-weight: 600;
		font-size: 30rpx;
		margin: 0 145rpx 40rpx;
	}

	.img-416 {
		width: 600rpx;
		height: 600rpx;
		margin: 0 75rpx 60rpx;
	}

	.previous-step {
		width: 260rpx;
		text-align: center;
		background-color: #FFF;
		padding: 30rpx 0;
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
		border-radius: 10rpx;
	}

	.add-text {
		width: 590rpx;
		border: 1px dashed rgb(30, 255, 139);
		border-radius: 10rpx;
		padding: 32rpx 0;
		color: #3AFA9A;
		margin: 40rpx 80rpx 30rpx;
	}

	.img-240 {
		width: 34rpx;
		height: 34rpx;
	}

	.img-94 {
		width: 30rpx;
		height: 30rpx;
	}

	.r-play {
		width: 70rpx;
		height: 70rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(76, 254, 124), rgb(2, 236, 252) 100%);
		margin-right: 20rpx;
	}

	.list-public {
		background-color: #1C2022;
		padding: 30rpx 20rpx;
	}

	.batc-font-2 {
		background-color: #282828;
		color: #E1E1E1;
	}

	.batc-font-1 {
		background: linear-gradient(114.62deg, rgb(90, 239, 87) 3.431%, rgb(128, 254, 234) 93.999%);
		color: #000;
	}

	.batc-font {
		width: 140rpx;
		text-align: center;
		border-radius: 10rpx;
		margin-right: 30rpx;
		font-weight: 500;
		padding: 12rpx 0;
	}

	.img-60 {
		position: absolute;
		z-index: 9;
		top: 10rpx;
		right: 20rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.p-but {
		width: 710rpx;
		padding: 24rpx 0;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		margin: 30rpx 20rpx 10rpx;
		font-size: 32rpx;
		color: #FFF;
		text-align: center;
	}

	.line-top-bot-sel {
		background: rgb(113, 223, 242);
	}

	.line-top-bot {
		width: 60rpx;
		height: 8rpx;
		border-radius: 10rpx;
		margin-top: 16rpx;
	}

	.line-top-name-sel {
		background: linear-gradient(180.00deg, rgb(156, 252, 124), rgb(113, 223, 242));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-size: 32rpx;
		font-weight: 600;
		letter-spacing: 0%;
	}

	.line-top {
		width: 750rpx;
		padding: 30rpx 0 22rpx;
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		// background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		border: 1px solid rgb(151, 249, 135);
		color: #97F987;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 10rpx;
	}

	.p-title {
		font-size: 32rpx;
		padding-bottom: 10rpx;
		text-align: center;
	}

	.get-close {
		font-size: 32rpx;
		font-weight: 500;
		text-align: right;
		padding: 0 30rpx;
	}

	.img-417 {
		width: 24rpx;
		height: 24rpx;
		margin-left: 4rpx;
	}

	.img-215 {
		width: 40rpx;
		height: 40rpx;
	}

	.add-img {
		width: 710rpx;
		border: 1px dashed rgb(30, 255, 139);
		margin: 0 20rpx 20rpx;
		border-radius: 10rpx;
		padding: 90rpx 0;
	}

	.line-batc {
		width: 8rpx;
		height: 28rpx;
		background-color: #5FF16B;
		margin-right: 20rpx;
	}

	.decode-img {
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
		// position: absolute;
		// z-index: 2;
		// top: 50%;
		// left: 50%;
		// transform: translate(-50%, -50%);
	}

	.img-213 {
		position: absolute;
		z-index: 9;
		bottom: 10rpx;
		right: 20rpx;
		width: 50rpx;
		height: 50rpx;
		background-color: #FFF;
		border-radius: 50%;
	}

	.img-211 {
		width: 230rpx;
		height: 409rpx;
		margin-left: 15rpx;
	}

	.c-img {
		position: relative;
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
		margin-left: 15rpx;
		margin-bottom: 15rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: inline-table;

	}

	.but-next {
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(89, 238, 82), rgb(130, 255, 242) 100%);
		padding: 30rpx 0;
		font-size: 32rpx;
		font-weight: 600;
	}

	.pos-bott {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		padding: 20rpx 20rpx 30rpx;
		z-index: 99;
		background-color: #111317;
	}

	.line-top-bot-sel {
		background: rgb(113, 223, 242);
	}

	.line-top-bot {
		width: 60rpx;
		height: 8rpx;
		border-radius: 10rpx;
		margin-top: 16rpx;
	}

	.line-top-name-sel {
		background: linear-gradient(180.00deg, rgb(156, 252, 124), rgb(113, 223, 242));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-size: 32rpx;
		font-weight: 600;
		letter-spacing: 0%;
	}

	.line-top {
		width: 750rpx;
		padding: 30rpx 0 22rpx;
	}

	.img-413 {
		width: 710rpx;
		height: 142rpx;
		margin: 0 20rpx;
		margin-bottom: 14rpx;
	}

	.bg {
		width: 750rpx;
		height: 580rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 200rpx 0 0;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}

	page {
		border: none;
		background-color: #111317;
		width: 100%;
		overflow-x: hidden !important;
	}
</style>