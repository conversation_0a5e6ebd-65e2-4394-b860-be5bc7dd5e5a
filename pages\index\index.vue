<template>
	<view class="main">
		<image class="top-img" :src="imgUrl + 'n-1.png'" mode=""></image>
		<view :style="{
		  height: '' + heightSystemss + 'px',
		  top: '' + statusBarHeightss + 'px',
		  lineHeight: '' + heightSystemss + 'px',
		  left: '' + 10 + 'px',
		}" class="iconDizhssi"></view>
		<block>
			<swiper class="swiper" :autoplay="autoplay" :circular="true" :interval="interval" :duration="duration">
				<swiper-item v-for="(item,index) in banner" :key="index">
					<image class="pic-img" :src="item.pic_url" @click="changeUrl(index,item)"></image>
				</swiper-item>
			</swiper>
		</block>

		<view class="content">

			<view class="feature">
				<block v-for="(item, index) in navigationList" :key="index">
					<view class="feature-item" @click="changeUrl(index, item)">
						<image class="feature-img" :src="item.pic_url"></image>
						<view class="color_FFFFFF">{{ item.name }}</view>
					</view>
				</block>
			</view>

			<view class="project display-fw-a" @click="getAdd2({id: 11, title: 'AI立项'})">
				<image class="img" :src="imgUrl + 'n-2.png'" mode=""></image>
				<view class="right display-fw-a">
					<view class="color_FFFFFF">专属我的流量工厂</view>
					<image class="img" :src="imgUrl + 'n-3.png'" mode=""></image>
				</view>
			</view>

			<view class="list" v-for="item in list" :key="item.id">
				<view class="list-title">{{item.title}}</view>
				<template v-if="item.id === 1">
					<view class="grid-style">
						<image v-for="(it, index) in serviceList" :key="index" class="img-2"
							:class="index === 0 ? 'big-img-2' : ''"
							:style="index === 0 ? 'grid-area: item1;' : index === 1 ? 'grid-area: item2;' : 'grid-area: item3;'"
							@click="changeUrl(index, it)" :src="it.pic_url"></image>
					</view>
				</template>
				<template v-else-if="[2,3,4].includes(item.id)">
					<view class="list2 display-fw-a">
						<view class="card-bgc list2-item display-fw-a" @click="getAdd(it)" v-for="it in item.children"
							:key="it.id">
							<image class="img" :src="imgUrl + it.img" mode=""></image>
							<view class="color_FFFFFF">{{ it.title }}</view>
						</view>
					</view>
				</template>
				<!-- <template v-else-if="item.id === 3">
					<view class="list3 display-fw-a">
						<view class="list3-item display-fw-a card-bgc" @click="getAdd(it)" v-for="it in item.children"
							:key="it.id">
							<view class="color_FFFFFF">
								<view>{{ it.title }}</view>
								<view class="desc">{{ it.desc }}</view>
							</view>
							<image class="img" :src="imgUrl + it.img" mode=""></image>
						</view>
					</view>
				</template>
				<template v-else-if="item.id === 4">
					<view class="list3 display-fw-a">
						<view class="list3-item display-fw-a card-bgc" @click="getAdd(it)" v-for="it in item.children"
							:key="it.id">
							<view class="color_FFFFFF">
								<view>{{ it.title }}</view>
								<view class="desc">{{ it.desc }}</view>
							</view>
							<image class="img" :src="imgUrl + it.img" mode=""></image>
						</view>
					</view>
				</template> -->
				<template v-else-if="item.id === 5">
					<view class="list5 display-fw-a card-bgc">
						<view class="list5-item display-fw-a" @click="getAdd(it)" v-for="it in item.children"
							:key="it.id">
							<image class="img" :src="imgUrl + it.img" mode=""></image>
							<view class="color_FFFFFF">
								<view>{{ it.title }}</view>
								<view class="desc">{{ it.desc }}</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</view>

		<sunui-tabbar :fixed="true" :current="tabIndex" :types="1"></sunui-tabbar>

		<block v-if="customerConfig.customer_type == 'on_line'">
			<button open-type="contact">
				<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl + 'kefu.gif'">
				</image>
			</button>
		</block>
		<block v-else>
			<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl + 'kefu.gif'">
			</image>
		</block>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image show-menu-by-longpress class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop6">
			<template v-slot:content>
				<view style="overflow: auto; padding: 20rpx">
					<scroll-view :scroll-y="true" style="height: 600rpx">
						<rich-parser :html="kfSet.upload_describe"
							domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load ref="article" selectable
							show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</scroll-view>
				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="rescuePop">
			<template v-slot:content>
				<image @click="getNewUser()" class="img-new" :src="userSet.new_pic"></image>
			</template>
		</sunui-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {

				isPrivacy: uni.getStorageSync("privacy") ? false : true,

				isOpen: '', // 1未弹出 2已弹出

				tabIndex: 0,

				// 轮播图
				indicatorDots: true,
				autoplay: true,
				interval: 4000,
				duration: 500,

				heightSystemss: '',
				statusBarHeightss: '',

				banner: [],

				imgUrl: this.$imgUrl,

				systems: {},

				userSet: {}, //新人设置

				user: {},

				windowHeight: '',

				navigationList: [], //分类列表
				serviceList: [], //服务列表


				isWhether: true, //判断重复点击

				is_open: '', //分销 1开启

				twoOpen: '2', // 线路二 1开启

				partner_is_open: '', //合伙人 1开启

				template_type: '', //首页模板

				cloneSet: {},

				isKefu: true, //true隐藏 false展开

				customerConfig: {}, //客服配置

				kfSet: {}, //更新公告

				imgList: [],

				// 功能列表
				list: [{
						id: 1,
						title: '公域 · 视频量产'
					},
					{
						id: 2,
						title: '矩阵 · 全域分发',
						children: [{
							id: 1,
							img: 'n-4.png',
							title: '爆款标题',
							desc: ''
						}, {
							id: 2,
							img: 'n-5.png',
							title: '矩阵管理',
							desc: ''
						}]
					},
					{
						id: 3,
						title: '直播 · 24小时',
						children: [{
							id: 3,
							img: 'n-19.png',
							title: 'AI直播',
							desc: ''
						}, {
							id: 11,
							img: 'n-14.png',
							title: 'AI拦截',
							desc: ''
						}]
					},
					{
						id: 4,
						title: '私域 · 精细运营',
						children: [{
							id: 4,
							img: 'n-15.png',
							title: '自动加微',
							desc: 'AI辅助自动化精细运营'
						}, {
							id: 12,
							img: 'n-16.png',
							title: '精准群发',
							desc: ''
						}, {
							id: 13,
							img: 'n-17.png',
							title: '朋友圈运营',
							desc: ''
						}, {
							id: 14,
							img: 'n-18.png',
							title: '营销智库',
							desc: ''
						}]
					},
					{
						id: 5,
						title: 'AI员工 · 降本增效',
						children: [{
							id: 5,
							img: 'n-8.png',
							title: 'AI设计',
							desc: ''
						}, {
							id: 6,
							img: 'n-9.png',
							title: '图片精修',
							desc: ''
						}, {
							id: 7,
							img: 'n-10.png',
							title: '表情包',
							desc: ''
						}, {
							id: 8,
							img: 'n-11.png',
							title: '商品抠图',
							desc: ''
						}, {
							id: 9,
							img: 'n-12.png',
							title: 'AI翻译官',
							desc: ''
						}, {
							id: 10,
							img: 'n-13.png',
							title: '图生视频',
							desc: ''
						}]
					}
				]

			}
		},

		onLoad(options) {
			this.getNavigation();
			this.getService();
			this.getSystemInfo();
			this.getBanner();
			this.indexkfSet();
			if (options.scene) {
				let scene = options.scene;
				uni.setStorageSync('pid', scene);
			}
			if (uni.getStorageSync('uid')) {
				this.userInfo();
			}
		},

		onShow() {

			this.getCustomerConfig();
			this.getSystem();
			this.getTallySet();
			this.getBrokerageSet();
			this.getPartnerSet();
			this.getCloneSet();
			this.getIndexWay();
		},

		methods: {
			async getAdd2(item) {
				if (item.id === 11) {
					// uni.navigateTo({
					// 	url: '/pages/index/ipGather/ipGather'
					// })
					uni.navigateTo({
						url: `/subPackages/subPackageA/twoPage?title=${item.title}&type=${item.id}`,
					});
				}
			},
			async getAdd(item) {
				if (uni.getStorageSync("uid")) {
					if (item.id === 1) {
						uni.navigateTo({
							url: `/pages/index/AICreation/aiTitle`,
						});
					} else if (item.id === 2) {
						uni.navigateTo({
							url: "/pages/matrix/matrix",
						});
					} else if (item.id === 5) {
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=${14}`,
						});
					} else if (item.id === 6) {
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=17`,
						});
					} else if (item.id === 7) {
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=18`,
						});
					} else if (item.id === 8) {
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=19`,
						});
					} else if (item.id === 9) {
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=16`,
						});
					} else if (item.id === 10) {
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=20`,
						});
					} else {
						this.$sun.toast('开发中', 'none');
						return
						uni.navigateTo({
							url: "/subPackages/subPackageA/generationsImg?type=" + 1 + "&title=内容选题",
						});
					}
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pages/auth/auth?type=1",
								});
							} else if (res.cancel) {
								// this.navig();
							}
						},
					});
				}
			},

			getOpen() {
				this.$refs.pop6.show({
					style: "background-color:#fff;width:600rpx;border-radius:10rpx;",
					bottomClose: true,
					shadeClose: false,
				});
			},

			// 客服设置接口
			async indexkfSet() {
				const resolu = await this.$http.get({
					url: this.$api.kfSet
				})
				if (resolu.errno == 0) {
					this.kfSet = resolu.data;
					if (this.kfSet.upload_describe_swich == 1 && uni.getStorageSync("isOpen") != 2) {
						uni.setStorageSync('isOpen', 2);
						this.getOpen();
					}
				} else {
					this.$sun.toast(resolu.message, "none")
				}
			},

			getKefu() {
				if (this.isKefu) {
					this.isKefu = false;
				} else {
					if (this.customerConfig.customer_type == 'on_line') {
						return;
					} else if (this.customerConfig.customer_type == 'phone') {
						if (this.customerConfig.customer_phone) {
							this.$sun.phone(this.customerConfig.customer_phone);
						} else {
							this.$sun.toast("暂无联系方式", 'error');
						}
					} else if (this.customerConfig.customer_type == 'qr_code') {
						this.$refs.pop5.show({
							style: 'background-color:#000;width:600rpx;border-radius:10rpx;',
							bottomClose: true,
							shadeClose: false,
						});
					}
				}
			},

			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
					uni.setStorageSync('cloneSet', result.data);
				}
			},

			//线路自定义名称
			async getIndexWay() {
				const result = await this.$http.post({
					url: this.$api.indexWay
				});
				if (result.errno == 0) {
					for (let i = 0; i < result.data.length; i++) {
						if (result.data[i].id == 2) {
							this.twoOpen = 1;
						}
					}
					uni.setStorageSync('indexWay', result.data);
				}
			},

			//客服配置
			async getCustomerConfig() {
				const result = await this.$http.post({
					url: this.$api.customerConfig
				});
				if (result.errno == 0) {
					this.customerConfig = result.data;
					uni.setStorageSync('customerConfig', result.data);
				}
			},

			//扣点设置
			async getTallySet() {

				const result = await this.$http.post({
					url: this.$api.tallySet
				});
				if (result.errno == 0) {
					uni.setStorageSync('tallySetObj', result.data);
				}
			},

			//导航
			async getNavigation() {
				const result = await this.$http.post({
					url: this.$api.navigation
				});
				if (result.errno == 0) {
					this.navigationList = result.data;
				}
			},

			//分销设置
			async getBrokerageSet() {
				const result = await this.$http.post({
					url: this.$api.brokerageSet,
				});
				if (result.errno == 0) {
					this.is_open = result.data.is_open;
				}
			},

			//合伙人设置
			async getPartnerSet() {
				const result = await this.$http.post({
					url: this.$api.partnerSet
				});
				if (result.errno == 0) {
					this.partner_is_open = result.data.is_open;
				}
			},

			//导航
			async getService() {
				const result = await this.$http.post({
					url: this.$api.service
				});
				if (result.errno == 0) {
					this.serviceList = result.data;
				}
			},

			//领取新人奖励
			async getNewUser() {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.userNewGet,
					data: {
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.$refs.rescuePop.close();
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.userInfo();
						this.isWhether = true;
					}, 2000);

				} else {
					this.$refs.rescuePop.close();
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//轮播图
			async getBanner() {
				const result = await this.$http.post({
					url: this.$api.banner,
					data: {
						b_type: 1
					}
				});
				if (result.errno == 0) {
					this.banner = result.data;
				}
			},

			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
					uni.setStorageSync('isMember', result.data.is_member);
					if (result.data.is_new == 1) {
						this.getUserSet();
					}
				}
			},

			//新人设置
			async getUserSet() {
				const result = await this.$http.post({
					url: this.$api.userSet
				});
				if (result.errno == 0) {
					this.userSet = result.data;
					if (this.userSet.new_open == 1) {
						this.newPop();
					}
				}
			},

			//新人弹窗
			newPop() {
				this.$refs.rescuePop.show({
					style: 'width:700rpx; height:650rpx;border-radius: 5px;',
					anim: 'center',
					shadeClose: false, //使用户不能点击其它关闭页面
					bottomClose: true,
					rgba: 'rgba(50,50,50,.6)'
				});
			},

			//自定义跳转
			changeUrl(index, values) {
				if (values.type == 1) {
					if (values.url == "/pages/my/partner/index") {
						if (this.partner_is_open != 1) {
							uni.showModal({
								content: "该功能暂未开放,尽请期待!",
								confirmText: "确认",
								showCancel: false,
								success: (res) => {
									if (res.confirm) {} else if (res.cancel) {}
								},
							});
							return;
						}
						this.getPartner();
					} else {
						if (
							values.url == "/pages/my/distribution/distribution" &&
							this.is_open != 1
						) {
							uni.showModal({
								content: "该功能暂未开放,尽请期待!",
								confirmText: "确认",
								showCancel: false,
								success: (res) => {
									if (res.confirm) {} else if (res.cancel) {}
								},
							});
							return;
						}

						// if (values.url === "/pages/index/AICreation/AICreation") {
						// 	this.getInfo((result) => {
						// 		if (!result.data) {
						// 			uni.showModal({
						// 				content: "使用AI文案，请先填写个人信息",
						// 				confirmText: "确认",
						// 				success: (res) => {
						// 					if (res.confirm) {
						// 						uni.navigateTo({
						// 							url: "/pages/my/userInfo",
						// 						});
						// 					} else if (res.cancel) {}
						// 				},
						// 			});
						// 		} else {
						// 			uni.navigateTo({
						// 				url: "/pages/index/AICreation/AICreation",
						// 			});
						// 		}
						// 	});
						// 	return;
						// }

						uni.navigateTo({
							url: values.url,
						});
					}
				}
				if (values.type == 2) {
					if (values.appid) {
						wx.navigateToMiniProgram({
							appId: values.appid,
							success: (res) => {
								// 打开成功
								// console.log('成功', res);
							},
							fail: (err) => {
								// console.log('失败', err);
							},
						});
					} else {
						uni.navigateTo({
							url: '/subPackages/subPackageA/webView?targetUrl=' + values.url
						})
						// this.$sun.toast("请检查跳转外部小程序的APPID是否正确", "none");
					}
				}
			},

			// 是否填写个人信息
			async getInfo(succCallBack) {
				const result = await this.$http.get({
					url: this.$api.getUserInfo,
					data: {
						uid: uni.getStorageSync("uid"),
					},
				});
				if (result.errno == 0) {
					succCallBack(result);
				} else {
					this.$sun.toast(result.message, "none");
				}
			},

			//合伙人
			getPartner() {
				if (uni.getStorageSync("uid")) {
					//正常
					if (this.user.partner_status == 2 && this.user.partner_freeze == 1) {
						uni.setStorageSync("partnerId", this.user.partner_id);
						uni.navigateTo({
							url: "/pages/my/partner/index",
						});
					}
					//冻结
					if (this.user.partner_status == 2 && this.user.partner_freeze == 2) {
						uni.showModal({
							content: "您的合伙人已冻结,如有疑问请联系管理员!",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {

								} else if (res.cancel) {

								}
							}
						})
					}
					//未申请
					if (this.user.partner_status == 0) {
						uni.navigateTo({
							url: '/pages/my/partner/partner'
						})
					}
					//审核中
					if (this.user.partner_status == 1) {
						uni.showModal({
							content: "您的申请正在审核中,请耐心等待!",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {

								} else if (res.cancel) {

								}
							}
						})
					}
					//驳回
					if (this.user.partner_status == 3) {
						uni.showModal({
							title: '审核驳回',
							content: this.user.partner_refuse,
							cancelText: "确认",
							confirmText: "重新申请",
							success: (res) => {
								if (res.confirm) {
									this.updataStatus();
								} else if (res.cancel) {

								}
							}
						})
					}

				} else {
					uni.navigateTo({
						url: '/pages/auth/auth?type=1'
					})
				}
			},

			//合伙人状态变更
			async updataStatus() {
				const result = await this.$http.post({
					url: this.$api.partnerUpdateStatus,
					data: {
						partner_id: this.user.partner_id,
						name: this.user.partner_name,
						telphone: this.user.partner_telphone
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message, 'none');
				} else {
					this.$sun.toast("认证状态变更失败", 'none');
				}
			},

			//系统设置
			async getSystem() {
				const result = await this.$http.post({
					url: this.$api.system
				});
				if (result.errno == 0) {
					this.systems = result.data;
					this.$sun.title(this.systems.name);
					uni.setStorageSync('system', result.data);
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni
					.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						// this.windowHeight = res.windowHeight * 2 - 920;
					},
					fail(err) {
						// console.log(err);
					},
				});
			},
		},
	};
</script>

<style lang="scss">
	.main {
		position: relative;
		width: 100%;
		height: 100%;

		.swiper {
			width: 750rpx;
			height: 420rpx;
			// margin-top: 40rpx;
			padding: 0 32rpx;


			.pic-img {
				width: 686rpx;
				height: 420rpx;
				border-radius: 32rpx;
			}
		}

		.top-img {
			position: absolute;
			top: 0;
			width: 100%;
			height: 800rpx;
		}

		.content {
			padding: 32rpx 48rpx;
			background: none;
			z-index: 9;

			.card-bgc {
				padding: 32rpx;
				background: #2E324D;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
			}

			.list {
				margin-bottom: 64rpx;

				.list5 {
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					gap: 32rpx;

					&-item {
						flex-direction: column;
						font-size: 24rpx;

						.img {
							width: 88rpx;
							height: 88rpx;
						}
					}
				}

				.list3 {
					&-item {
						justify-content: space-between;
						flex: 1;
						padding: 0 0 0 32rpx;

						.desc {
							font-size: 22rpx;
							color: rgba(255, 255, 255, 0.5);
							margin-top: 8rpx;
						}
					}

					.img {
						width: 144rpx;
						height: 144rpx;
					}
				}

				.list2 {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					gap: 32rpx;

					&-item {
						font-size: 28rpx;
						flex: 1;

						.img {
							width: 64rpx;
							height: 64rpx;
							margin-right: 32rpx;
						}
					}
				}



				.grid-style {
					display: grid;
					grid-template-columns: 315rpx 315rpx;
					grid-template-rows: auto auto;
					grid-template-areas:
						"item2 item1"
						"item3 item1";
					gap: 14rpx;

					.img-2 {
						width: 100%;
						height: 144rpx;
						margin-bottom: 0;
					}

					.big-img-2 {
						height: 302rpx;
						/* 两个普通图片高度加上间隔 */
						object-fit: cover;
						margin-left: 10rpx;
					}
				}

				&-title {
					font-weight: 600;
					font-size: 36rpx;
					color: #FFFFFF;
					margin-bottom: 34rpx;
				}
			}

			.project {
				position: relative;
				width: 654rpx;
				height: 76rpx;
				background: linear-gradient(180deg, rgba(1, 5, 30, 0.5) 0%, #01051E 100%);
				border-radius: 16rpx;
				padding: 0 16rpx;
				justify-content: space-between;
				margin-bottom: 48rpx;
				z-index: 99;

				.right {
					font-size: 24rpx;

					.img {
						width: 32rpx;
						height: 32rpx;
						margin-left: 16rpx;
					}
				}

				.img {
					width: 112rpx;
					height: 44rpx;
				}
			}

			.feature {
				display: flex;
				justify-content: space-between;
				padding: 0 32rpx;
				margin-bottom: 40rpx;

				&-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
				}

				&-img {
					width: 88rpx;
					height: 82rpx;
					margin-bottom: 8rpx;
				}
			}
		}
	}

	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.kefu-2 {
		right: -50rpx;
		opacity: 0.5;
		transition: all 1s linear;
	}

	.kefu-1 {
		right: 10rpx;
		transition: all 1s linear;
	}

	.kefu {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		z-index: 99;
		// right: -50rpx;
		bottom: 340rpx;
	}

	.img-new {
		width: 700rpx;
		height: 650rpx;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #202336;
	}
</style>