<template>
	<view>

		<view class="padding_30rpx">
			<liu-step-bar :step="stepIndex" :stepList="stepList" @clickStep="clickStep"></liu-step-bar>
		</view>
		<view class="h_20rpx"></view>

		<view class="s-frame">

			<view class="display-a-js">
				<view class="display-ac-jc">
					<view class="bg" :style="{'background-image': 'url('+imgUrl+'223.png'+')'}">
						<view class="video-play" style="position: relative;" v-if="video_url">
							<image class="img-270" @click="delVideo()" :src="imgUrl+'270.png'"></image>
							<video class="video-play" style="margin: 12rpx 0 0 12rpx;" :src="video_url"></video>
						</view>
						<view class="display-ac-jc m-pa" @click="addVideoImg()" v-else>
							<image class="img-226" :src="imgUrl+'226.png'"></image>
							<view class="color_64F2FB">点击上传视频(必选)</view>
							<view class="font-size_24rpx color_999999">上传一段30秒以上视频，经过训练即可生成高端逼真的高清数字人分身</view>
						</view>
					</view>
					<view class="display-a margin-top_20rpx" @click="getBatch()" v-if="twoOpen == 1">
						<view style="color: #35DDEB;">选择模板</view>
						<image class="img-62" :src="imgUrl+'62.png'"></image>
					</view>
				</view>
				<image class="img-248" mode="widthFix" :src="imgUrl+'248.png'"></image>
			</view>
		</view>

		<view class="display-a padding_0_20rpx">
			<view class="a-line"></view>
			<view class="a-tilte">一键分身三步骤</view>
			<image class="img-243" :src="imgUrl+'243.png'"></image>
		</view>

		<view class="padding_20rpx">
			<view class="display-a margin-bottom_30rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">第一步: 上传形象视频</view>
			</view>
			<view class="display-a margin-bottom_30rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">第二步: 上传或录制一段语音</view>
			</view>
			<view class="display-a margin-bottom_30rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">第三步: 直接提交视频合成</view>
			</view>
		</view>

		<view style="height: 160rpx;"></view>

		<view class="bott-pos">
			<view class="s-but" @click="getBut()">下一步</view>
		</view>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="color_FFFFFF padding_30rpx_0" style="position: relative;">
					<image @click="closeBatch()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">我的形象</view>
					<scroll-view :scroll-x="true" style="width: 730rpx; white-space: nowrap;">
						<view class="display-a">
							<block v-if="avatarList.length > 0">
								<block v-for="(item,index) in avatarList" :key="index">
									<view class="c-img" @click="confirmImage(item)" :id="'id'+item.id">
										<image class="img-213"
											:src="item.id == videoId ? imgUrl+'213.png' : imgUrl+'212.png'"></image>
										<image class="decode-img" mode="widthFix" :src="item.video_cover"></image>
									</view>
								</block>
							</block>
							<block v-else>
								<view class="display-ac-jc" style="margin-left: 160rpx;">
									<image class="nodata" src="../../../static/nodata.png"></image>
									<view class="nodata-tips">您还没有数字人，快去定制一个吧~</view>
									<view class="nodata-but" @click="getClone()">创建数字人</view>
								</view>
							</block>
						</view>
					</scroll-view>
					<view v-if="avatarList.length > 0" class="p-but" @click="nextBut()">下一步</view>

				</view>
			</template>
		</sunui-popup>

	</view>
</template>

<script>
	const base64 = require('@/utils/ali-oos/base64.js'); //Base64,hmac,sha1,crypto相关算法
	require('@/utils/ali-oos/hmac.js');
	require('@/utils/ali-oos/sha1.js');
	const Crypto = require('@/utils/ali-oos/crypto.js');
	export default {
		data() {
			return {

				stepList: [{
					name: '1.形象克隆',
					id: 1
				}, {
					name: '2.语音配置',
					id: 2
				}, {
					name: '3.提交合成',
					id: 3
				}], //步骤列

				stepIndex: 1,

				imgUrl: this.$imgUrl,

				video_url: '', //视频路径

				videoId: '', //选择形象ID
				video_cover: '', //

				setLine: '', //1 线路1 2线路2  3线路3
				current_status: '',
				new_current_status: '',

				avatarList: [],

				cloneSet: uni.getStorageSync("cloneSet"), //克隆开关设置

				upPicUrl2: '',

				progress: 0, //上传视频进度条

				formData: {
					'key': '',
					'policy': '',
					'OSSAccessKeyId': '',
					'signature': '',
					'success_action_status': '200',
				},

				policyText: {
					"expiration": "2030-01-01T12:00:00.000Z", //设置该Policy的失效时间，超过这个失效时间之后，就没有办法通过这个policy上传文件了
					"conditions": [
						["content-length-range", 0, 104857600] // 设置上传文件的大小限制    209715200
					]
				},

				isWhether: true, //判断重复点击

				isKefu: true, //true隐藏 false展开

				customerConfig: uni.getStorageSync('customerConfig'), //客服配置

				setLineList: uni.getStorageSync('indexWay'), //已开启的线路

				twoOpen: '2', //线路二 1开启


			}
		},

		onLoad() {
			this.getAliyunConfig();
			for (let i = 0; i < this.setLineList.length; i++) {
				if (this.setLineList[i].id == 2) {
					this.twoOpen = 1;
				}
			}
		},

		onShow() {
			if (uni.getStorageSync('uid')) {
				this.getAvatarList();
			}
		},

		methods: {


			nextBut() {

				if (!this.videoId) {
					this.$sun.toast("请先选择形象", 'error');
					return;
				}

				uni.navigateTo({
					url: '/pages/index/synthesis/voiceClip?videoId=' + this.videoId + '&video_cover=' + this
						.video_cover
				})

				this.closeBatch();
			},

			//确认形象
			confirmImage(obj) {
				this.videoId = obj.id;
				this.video_cover = obj.video_url;
			},

			getBatch() {
				if (uni.getStorageSync('uid')) {
					this.$refs.pop3.show({
						style: 'background-color:#000;width:750rpx;',
						anim: 'bottom',
						position: 'bottom',
						shadeClose: false,
						rgba: 'rgba(50,50,50,.6)'
					});
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {

							}
						}
					})
				}
			},
			closeBatch() {
				this.$refs.pop3.close();
			},

			//我的形象
			async getAvatarList() {

				let getUrl = '';

				getUrl = this.$api.avatarList;

				const result = await this.$http.post({
					url: getUrl,
					data: {
						uid: uni.getStorageSync('uid'),
						new_current_status: 'completed',
						buy_expire: 2, //资产市场购买 2未过期
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {
					this.avatarList = result.data.list;
				}
			},

			getClone() {
				uni.navigateTo({
					url: '/pages/index/clone/clone'
				})
			},

			delVideo() {
				this.video_url = '';
			},

			getBut() {
				if (this.stepIndex == 1) {

					if (uni.getStorageSync('uid')) {
						if (!this.video_url) {
							this.$sun.toast("请先上传视频", 'error');
							return;
						}

						if (!this.isWhether) {
							return;
						}
						this.isWhether = false;

						this.getStartTraining();
						// this.stepIndex = 2;
					} else {
						uni.showModal({
							content: "请先登录",
							cancelText: "取消",
							confirmText: "去登录",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/auth/auth?type=1'
									})
								} else if (res.cancel) {

								}
							}
						})
					}

				}
			},

			//形象开始训练
			async getStartTraining() {

				let getUrl = '';
				let getData = '';

				getUrl = this.$api.towTraining;
				getData = {
					uid: uni.getStorageSync('uid'),
					video_url: this.video_url,
					is_avatar: 1
				}

				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					// this.$sun.toast(result.message);
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/index/synthesis/voiceClip?videoId=' + result.data.id +
								'&video_cover=' + result.data.video_url
						})
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			//点击步骤
			clickStep(e) {
				// console.log('所点击步骤信息:', e)
				// this.stepIndex = e.id;
			},

			/*  阿里云设置  */
			async getAliyunConfig() {
				const result = await this.$http.post({
					url: this.$api.aliyunConfig
				});
				if (result.errno == 0) {
					this.upPicUrl2 = 'https://' + result.data.alioss_domain;
					this.formData.OSSAccessKeyId = result.data.alioss_access_key_id;

					this.formData.policy = base64.encode(JSON.stringify(this.policyText));
					let message = this.formData.policy;
					let bytes = Crypto.HMAC(Crypto.SHA1, message, result.data.alioss_access_key_secret, {
						asBytes: true
					});
					this.formData.signature = Crypto.util.bytesToBase64(bytes);
				}
			},

			//视频上传
			addVideoImg() {

				// if (this.video_url) {
				// 	return;
				// }

				// 验证后端
				if (!this.upPicUrl2) {
					uni.showToast({
						title: '请配置阿里云',
						icon: 'none'
					});
					return;
				}

				uni.chooseVideo({
					count: 1,
					compressed: false,
					sourceType: ['album'],
					success: res => {
						let file = res.tempFilePath;
						let suffix = 'mp4';
						if (res.tempFilePath) {
							suffix = res.tempFilePath.split(".");
						} else {
							this.$sun.toast("视频资源异常,请重新选择!", 'none');
							return;
						}
						let maxSize = 100;
						if (res.size / 1024 / 1024 > maxSize) {
							this.$sun.toast(`视频不能超过${maxSize}M`, 'none');
							return;
						}

						this.uploadBaseVideo(file, suffix[suffix.length - 1]);

					},
					complete: function() {

					},
					fail: function(err) {
						console.log("uni.chooseVideo err---->", err);
					}
				});


			},

			//上传视频
			uploadBaseVideo(file, suffix) {

				this.video_url = '';

				// 设置一个变量来存储数字
				let count = 0;
				// 设置一个变量来存储定时器
				let timer = null;

				uni.showLoading({
					title: '上传中...' + count + '%',
					mask: true
				});

				// 设置定时器更新数字
				timer = setInterval(() => {
					// count = (count + 1) % 100; // 这里模拟0-99的变化
					uni.showLoading({
						title: `上传中... ${count}%`, // 使用字符串模板并格式化
					});
				}, 300); // 每0.1秒更新一次

				this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150) + '.' + suffix;

				console.log("this.formData.key---->", this.formData.key);

				// 创建上传对象
				const task = uni.uploadFile({
					url: this.upPicUrl2,
					filePath: file,
					fileType: 'video/mp4',
					name: 'file',
					formData: this.formData,
					header: {},
					success: uploadRes => {

						console.log('uploadRes', uploadRes);

						if (uploadRes.statusCode != 200) {
							uni.showToast({
								title: '上传失败 : ' + uploadRes.data,
								icon: 'none'
							});
							clearInterval(timer);
							uni.hideLoading();
						} else {
							count = 100;
							this.video_url = this.upPicUrl2 + '/' + this.formData.key;
							clearInterval(timer);
							uni.hideLoading();
						}
					},
					fail: e => {
						uni.showToast({
							title: '上传失败,' + e,
							icon: 'none'
						});
						clearInterval(timer);
						uni.hideLoading();
					}
				});
				task.onProgressUpdate(res => {
					if (res.progress > 0 && count < 100) {
						count = res.progress;
					}
				});
			},

		}
	}
</script>

<style lang="scss">
	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}

	.decode-img {
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
	}

	.img-213 {
		position: absolute;
		z-index: 9;
		bottom: 10rpx;
		right: 20rpx;
		width: 50rpx;
		height: 50rpx;
		background-color: #FFF;
		border-radius: 50%;
	}

	.c-img {
		position: relative;
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
		margin-left: 20rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: inline-table;

	}

	.p-title {
		font-size: 32rpx;
		padding-bottom: 30rpx;
		text-align: center;
	}

	.p-but {
		width: 710rpx;
		padding: 24rpx 0;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		margin: 30rpx 20rpx 10rpx;
		font-size: 32rpx;
		color: #FFF;
		text-align: center;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 30rpx;
		right: 30rpx;
	}

	.img-270 {
		width: 36rpx;
		height: 36rpx;
		position: absolute;
		z-index: 9;
		right: 8rpx;
		top: 20rpx;
	}

	.c-tips {
		background: #17DA70;
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 12rpx;
	}

	.img-62 {
		width: 36rpx;
		height: 36rpx;
		margin-top: 2rpx;
	}

	.bott-pos {
		width: 750rpx;
		position: fixed;
		bottom: 0;
		padding: 30rpx 50rpx;
	}

	.s-but {
		width: 650rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
	}

	.a-tilte {
		background: linear-gradient(90.00deg, rgb(53, 221, 235), rgb(63, 182, 254));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-weight: 700;
		letter-spacing: 0%;
		font-size: 34rpx;
	}

	.a-line {
		width: 6rpx;
		height: 26rpx;
		background-color: #35DDEB;
		border-radius: 4rpx;
		margin-right: 10rpx;
	}

	.img-243 {
		width: 82rpx;
		height: 48rpx;
	}

	.img-248 {
		width: 180rpx;
	}

	.m-pa {
		padding: 240rpx 50rpx 0;
	}

	.color_64F2FB {
		color: #64f2fb;
		font-size: 28rpx;
		font-weight: 600;
		margin-bottom: 20rpx;
	}

	.img-226 {
		width: 74rpx;
		height: 74rpx;
	}

	.video-play {
		width: 370rpx;
		height: 676rpx;
		border-radius: 30rpx;
	}

	.bg {
		width: 393rpx;
		height: 700rpx;
		background-repeat: no-repeat;
		background-size: contain;
	}

	.s-frame {
		width: 710rpx;
		background-color: #212429;
		border-radius: 10rpx;
		margin: 40rpx 20rpx 20rpx;
		padding: 24rpx 30rpx;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		background-color: #000;
		border: none;
	}
</style>