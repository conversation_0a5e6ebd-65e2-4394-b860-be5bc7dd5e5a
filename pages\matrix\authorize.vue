<template>
	<view>

		<sunui-popup ref="pop2">
			<template v-slot:content>
				<view class="display-a-jc padding_30rpx margin-bottom_30rpx">
					<view class="font-weight_bold font-size_32rpx">请选择发布渠道</view>
				</view>
				<view class="display-fw-a p-bo">
					<block v-for="(item,index) in tabs" :key="index">
						<view class="width_186rpx-center margin-bottom_30rpx" @click="setTabId(item.id)">
							<image class="matrix-17" :src="imgUrl+item.url"></image>
							<view>{{item.name}}</view>
						</view>
					</block>
				</view>
				<view class="cancel color_656565" @click="closeBatch()">取消</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="h_20rpx"></view>
				<view class="display-a-js padding_30rpx_0">
					<view class="font-size_32rpx">选择分组</view>
					<view class="color_0E8FF3" @click="getAddGroup()">＋添加分组</view>
				</view>
				<picker mode="selector" @change="bindPickerChange4" :range="groupList" :range-key="'name'">
					<view class="display-a input-name" style="width: 710rpx;">
						<input type="text" style="width: 680rpx;" v-model="selGroupName" disabled placeholder="请给账户选择分组"
							placeholder-class="placeholder" />
						<image class="matrix-20" :src="imgUrl+'21.png'"></image>
					</view>
				</picker>
				<block v-if="tabId == 1">
					<picker mode="multiSelector" @change="bindPickerChange5" @columnchange="bindDatePickerColumnChange"
						:value="cityIndex" :range="arrList" range-key="name">
						<view class="display-a input-name" style="width: 710rpx;">
							<input type="text" style="width: 680rpx;" v-model="provinceCity" disabled
								placeholder="请选择地区" placeholder-class="placeholder" />
							<image class="matrix-20" :src="imgUrl+'21.png'"></image>
						</view>
					</picker>
				</block>
				<view v-if="tabId == 3" class="display-a input-name" style="width: 710rpx;">
					<input type="text" style="width: 710rpx;" v-model="customize_account_name" placeholder="请输入账户名称"
						placeholder-class="placeholder" />
				</view>
				<block v-if="tabId == 4">
					<view class="display-a margin-bottom_40rpx">
						<image @click="getCookie(1)" class="img-374"
							:src="cookieType == 1 ? imgUrl+'375.png' : imgUrl+'374.png'"></image>
						<view @click="getCookie(1)" class="margin-right_40rpx">扫码绑定</view>
						<image @click="getCookie(2)" class="img-374"
							:src="cookieType == 2 ? imgUrl+'375.png' : imgUrl+'374.png'"></image>
						<view @click="getCookie(2)">小红薯cookie绑定</view>
					</view>
					<view v-if="cookieType == 2" class="display-a input-name" style="width: 710rpx;">
						<input type="text" style="width: 710rpx;" v-model="cookie" placeholder="请输入小红薯cookie"
							maxlength="-1" placeholder-class="placeholder" />
					</view>
				</block>


				<view class="display-a" style="border-top: 1px solid #F7F7F7;">
					<view @click="getGroupClose()" class="cancel color_656565" style="width: 374rpx;">取消</view>
					<view class="line-au"></view>
					<block v-if="tabId == 4">
						<view v-if="cookieType == 1" @click="getQrcode()" class="cancel color_0E8FF3"
							style="width: 354rpx;">确认</view>
						<view v-if="cookieType == 2" @click="getCookieAuth()" class="cancel color_0E8FF3"
							style="width: 354rpx;">确认授权</view>
					</block>
					<view v-else @click="getQrcode()" class="cancel color_0E8FF3" style="width: 354rpx;">
						确认
					</view>
				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<view class="bg" :style="{'background-image': 'url('+imgUrl+'393.png'+')'}">
					<view class="margin-bottom_20rpx font-size_32rpx">
						打开
						<block v-if="tabId == 1">D音</block>
						<block v-if="tabId == 2">K手</block>
						<block v-if="tabId == 3">微信</block>
						<block v-if="tabId == 4">小红薯</block>
						,登录授权
					</view>
					<view class="color_666666">
						打开「
						<block v-if="tabId == 1">抖音APP</block>
						<block v-if="tabId == 2">快手APP</block>
						<block v-if="tabId == 3">微信</block>
						<block v-if="tabId == 4">小红书APP</block>
						」点击左上角进行扫一扫
					</view>
				</view>

				<block v-if="tabId == 1 || tabId == 3">
					<view class="display-a-jc margin-bottom_30rpx" v-if="dateStuts == 1 || dateStuts == 3">
						<view class="get-date">00</view>
						<view style="margin-right: 10rpx;color: #000;">:</view>
						<view class="get-date">00</view>
						<view style="margin-right: 10rpx;color: #000;">:</view>
						<view class="get-date">{{remainingTime}}</view>
						<view>秒</view>
					</view>
				</block>

				<view class="qr-code-bor">
					<block v-if="tabId == 2">
						<image class="img-qr" :src="qrCode"></image>
					</block>
					<block v-else>
						<image @click="getQrcode()" v-if="dateStuts == 2" class="img-qr" :src="imgUrl+'398.png'">
						</image>
						<image v-if="dateStuts == 3" class="img-qr" :src="imgUrl+'399.png'"></image>
						<image v-if="dateStuts == 4" class="img-qr" :src="imgUrl+'400.png'"></image>
						<image v-if="dateStuts == 1" class="img-qr" :src="qrCode"></image>
					</block>

				</view>

				<view class="display-a input-name" style="width: 710rpx;margin-left: 20rpx;"
					v-if="tabId == 1 && dateStuts == 4">
					<input type="text" style="width: 710rpx;" v-model="yzmCode" placeholder="请输入验证码(已发送到扫码用户手机短信)"
						placeholder-class="placeholder" />
				</view>

				<view class="display-a" style="border-top: 1px solid #F7F7F7;">
					<view @click="getQrcodeClose()" class="cancel color_656565" style="width: 374rpx;">关闭</view>
					<view class="line-au"></view>
					<view v-if="tabId == 2" @click="longpress()" class="cancel color_0E8FF3" style="width: 354rpx;">
						保存到相册</view>
					<block v-if="tabId == 1">
						<view v-if="dateStuts == 4" @click="getDouyinVerifySms()" class="cancel color_0E8FF3"
							style="width: 354rpx;">完成授权</view>
						<view v-else @click="getQrcode()" class="cancel color_0E8FF3" style="width: 354rpx;">刷新二维码
						</view>
					</block>
					<block v-if="tabId == 3 || tabId == 4">
						<view v-if="dateStuts == 4" @click="getQrcodeClose()" class="cancel color_0E8FF3"
							style="width: 354rpx;">
							完成授权
						</view>
						<view v-else @click="getQrcode()" class="cancel color_0E8FF3" style="width: 354rpx;">
							刷新二维码
						</view>
					</block>
				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="display-a-js padding_30rpx p-bo">
					<view class="font-weight_bold font-size_32rpx" style="margin-left: 300rpx;">添加分组</view>
					<view class="color_FF0000" @click="getAddGroupClose()">关闭</view>
				</view>
				<view style="padding: 34rpx;">
					<view class="font-size_32rpx font-weight_bold margin-bottom_30rpx">分组类型</view>
					<view class="display-a margin-bottom_10rpx">
						<block v-for="(item,index) in tabs" :key="index">
							<view class="display-a width_170rpx-center margin-bottom_20rpx" @click="getType(item.id)">
								<image class="matrix-12"
									:src="groupType == item.id ? imgUrl+'375.png' : imgUrl+'374.png'"></image>
								<view>{{item.name}}</view>
							</view>
						</block>
					</view>
					<view class="font-size_32rpx font-weight_bold margin-bottom_30rpx">分组名称</view>
					<input type="text" class="input-name" placeholder="请输入分组名称" placeholder-class="placeholder"
						v-model="groupName" />
					<view class="list-but" @click="getAdd()">确认添加</view>
				</view>
			</template>
		</sunui-popup>

		<sunui-tabbar2 v-if="type !== 1" :fixed="true" :current="tabIndex" tintColor="#00FFCA"
			backgroundColor="#1B1B1B"></sunui-tabbar2>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				tabIndex: 2,

				imgUrl: this.$imgUrl,

				tabs: [{
						id: '1',
						name: 'D音',
						url: '378.png'
					},
					{
						id: '2',
						name: 'K手',
						url: '379.png'
					},
					{
						id: '3',
						name: '视频号',
						url: '380.png'
					},
					{
						id: '4',
						name: '小红薯',
						url: '381.png'
					},
					// {id:'5',name:'B站',url:'382.png'}
				],
				tabId: '1',

				groupType: 1, //1.D音 2.K手 3.视频号 4.小红薯 5.B站
				groupName: '', //分组名称

				isWhether: true, //判断重复点击

				groupList: [], //分组列表
				selGroupId: '', //分组ID
				selGroupName: '',

				customize_account_name: '', //视频号账户名称

				cookie: '', //小红书cookie
				cookieType: '1', //1 二维码 2 cookie绑定 

				qrCode: '',
				id: '',
				yzmCode: '', //抖音验证码

				province_id: '',
				province_name: '',
				city_id: '',
				city_name: '',
				provinceCity: '',
				cityList: [],
				cityIndex: [0, 0],
				arrList: [],

				remainingTime: 60,
				intervalId: '', //倒计时
				dateStuts: '1', //1显示 2过期 3扫码中 4扫码完成

				pollingId: '', //轮询

			}
		},

		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}

		},

		onShow() {
			this.getBatch();
		},

		onBackPress() {
			if (this.intervalId) {
				clearInterval(this.intervalId);
			}
			if (this.pollingId) {
				clearInterval(this.pollingId);
			}

		},

		onHide() {
			if (this.intervalId) {
				clearInterval(this.intervalId);
			}
			if (this.pollingId) {
				clearInterval(this.pollingId);
			}
		},

		methods: {

			//小红书cookie授权
			async getCookieAuth() {

				if (!this.selGroupId) {
					this.$sun.toast("请给账户选择分组", 'none');
					return;
				}

				if (!this.cookie) {
					this.$sun.toast("请输入小红书cookie", 'none');
					return;
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.xiaohongshuCookie,
					data: {
						uid: uni.getStorageSync("uid"),
						account_group_id: this.selGroupId,
						cookie: this.cookie
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.isWhether = true;
						uni.redirectTo({
							url: '/pages/matrix/account?tabId=' + this.tabId
						})
					}, 2000);
				} else {
					this.$sun.toast(result.message, 'none');
					this.isWhether = true;
				}

			},

			getCookie(type) {
				this.cookieType = type;
				this.cookie = '';
			},

			//完成抖音授权
			async getDouyinVerifySms() {

				if (!this.yzmCode) {
					this.$sun.toast("请输入验证码", 'none');
					return;
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.douyinVerifySms,
					data: {
						uid: uni.getStorageSync("uid"),
						id: this.id,
						code: this.yzmCode
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.isWhether = true;
						uni.redirectTo({
							url: '/pages/matrix/account?tabId=' + this.tabId
						})
					}, 2000);
				} else {
					this.$sun.toast(result.message, 'none');
					this.isWhether = true;
				}
			},

			//轮询调用检查
			polling() {
				this.pollingId = setInterval(() => {
					if (this.tabId == 1) {
						this.checkDy();
					}
					if (this.tabId == 3) {
						this.checkSph();
					}
					if (this.tabId == 4) {
						this.checkXhs();
					}
				}, 2000); // 
			},

			//检查小红书
			async checkXhs() {
				const result = await this.$http.post({
					url: this.$api.xiaohongshuUserInfo,
					loading: false,
					data: {
						uid: uni.getStorageSync("uid"),
						id: this.id
					}
				});
				if (result.errno == -4) { //未扫码
					this.dateStuts = 1;
				}
				if (result.errno == -5) { //扫码中
					this.dateStuts = 3;
				}
				if (result.errno == -3) { //已过期
					this.dateStuts = 2;
					clearInterval(this.pollingId);
				}
				if (result.errno == 0) { //扫码成功
					this.dateStuts = 4;
					clearInterval(this.pollingId);
				}
				if (result.errno == -1) { //授权失败

					clearInterval(this.pollingId);
				}
			},

			//检查视频号
			async checkSph() {
				const result = await this.$http.post({
					url: this.$api.shipinghaoeQrcodeCheck,
					loading: false,
					data: {
						uid: uni.getStorageSync("uid"),
						id: this.id
					}
				});
				if (result.errno == -3) {
					this.dateStuts = 1;
				}
				if (result.errno == 0) {
					this.dateStuts = 4;
					clearInterval(this.pollingId);
					clearInterval(this.intervalId);
				}
			},

			//检查抖音
			async checkDy() {
				const result = await this.$http.post({
					url: this.$api.douyinQrcodeCheck,
					loading: false,
					data: {
						uid: uni.getStorageSync("uid"),
						id: this.id
					}
				});
				if (result.errno == -4) { //扫码中
					this.dateStuts = 3;
				}
				if (result.errno == -1) { //已过期
					this.dateStuts = 2;
					clearInterval(this.pollingId);
				}
				if (result.errno == -3) { //未扫码
					this.dateStuts = 1;
				}
				if (result.errno == 0) { //扫码成功
					this.dateStuts = 4;
					clearInterval(this.pollingId);
					clearInterval(this.intervalId);
				}
			},

			//倒计时
			countDown() {
				this.dateStuts = 1;
				this.remainingTime = 60;
				this.intervalId = '';
				// var remainingTime = 60; // 倒计时的剩余时间，单位为秒
				this.intervalId = setInterval(() => {
					if (this.remainingTime > 0) {
						this.remainingTime--;
						console.log("剩余时间：" + this.remainingTime + "秒");
					} else {
						clearInterval(this.intervalId);
						if (this.tabId == 3) {
							clearInterval(this.pollingId);
						}
						this.dateStuts = 2;
						console.log("倒计时结束");
					}
				}, 1000); // 每秒执行一次
			},

			bindPickerChange5(e) {
				this.province_id = this.arrList[0][e.detail.value[0]].value;
				this.province_name = this.arrList[0][e.detail.value[0]].name;
				this.city_id = this.arrList[1][e.detail.value[1]].value;
				this.city_name = this.arrList[1][e.detail.value[1]].name;
				this.provinceCity = this.province_name + '-' + this.city_name;
				console.log("---->", e, this.city_id, this.arrList);
			},

			bindDatePickerColumnChange(e) {
				if (e.detail.column == 0) {
					this.cityIndex[0] = e.detail.value;
					this.cityIndex[1] = 0;
					this.setTime();
				}
				if (e.detail.column == 1) {
					this.cityIndex[1] = e.detail.value;
					this.setTime();
				}
			},

			//获取地区
			async getCity() {
				const result = await this.$http.post({
					url: this.$api.liuGuanProvince
				});
				if (result.errno == 0) {
					this.cityList = result.data;
					this.setTime();
				}
			},

			//预约时间
			setTime() {
				let range = [
					[],
					[]
				];
				let timeList = this.cityList;
				timeList.forEach(el => {
					range[0].push({
						name: el.label,
						value: el.value
					});
				});
				timeList[this.cityIndex[0]].children.forEach(el => {
					range[1].push({
						name: el.label,
						value: el.value
					});
				});
				// 更新数据
				this.arrList = range;
				this.$forceUpdate();
			},

			//保存到相册
			longpress() {
				uni.downloadFile({
					url: this.qrCode,
					success: (res) => {
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: (re) => {
								uni.showToast({
									title: '保存相册成功'
								});
								console.log('save success');
							},
							fail(error) {
								console.log('save fail', error);
								if (error.errMsg == 'saveImageToPhotosAlbum:fail auth deny') {
									uni.showModal({
										title: '您需要授权相册权限',
										success(res) {
											if (res.confirm) {
												uni.openSetting({
													success(res) {

													},
													fail(res) {
														console.log(res);
													}
												});
											}
										}
									});
								} else {
									if (error.errMsg == 'saveImageToPhotosAlbum:fail cancel') {

									} else {
										this.$uni.toast(error.errMsg, 'none');
									}
								}
							}
						});
					},
					fail: (err) => {
						console.log('downloadFile fail', err);
						this.$uni.toast(err, 'none');
					}
				});

			},

			//打开二维码
			getQrcodeOpen() {
				this.$refs.pop5.show({
					style: 'background-color:#fff;width:750rpx;height:auto;border-radius: 40rpx 40rpx 0 0',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			getQrcodeClose() {
				clearInterval(this.pollingId);
				clearInterval(this.intervalId);
				this.$refs.pop5.close();
				uni.redirectTo({
					url: '/pages/matrix/account?tabId=' + this.tabId
				})
				// this.getGroupOpen();
			},

			//获取二维码
			async getQrcode() {

				clearInterval(this.pollingId);
				clearInterval(this.intervalId);

				if (!this.selGroupId) {
					this.$sun.toast("请给账户选择分组", 'none');
					return;
				}

				if (this.tabId == 1) {
					if (!this.city_name) {
						this.$sun.toast("请选择地区", 'none');
						return;
					}
				}

				if (this.tabId == 3) {
					if (!this.customize_account_name) {
						this.$sun.toast("请输入账户名称", 'none');
						return;
					}
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				let getUrl = '';
				let getData = '';

				if (this.tabId == 1) {
					getUrl = this.$api.douyinQrcode;
					getData = {
						account_group_id: this.selGroupId,
						uid: uni.getStorageSync("uid"),
						province_id: this.province_id,
						city_id: this.city_id
					}
				}

				if (this.tabId == 2) {
					getUrl = this.$api.kuaishouQrcode;
					getData = {
						account_group_id: this.selGroupId,
						uid: uni.getStorageSync("uid"),
					}
				}

				if (this.tabId == 3) {
					getUrl = this.$api.shipinghaoQrcode;
					getData = {
						account_group_id: this.selGroupId,
						uid: uni.getStorageSync("uid"),
						customize_account_name: this.customize_account_name,
						province_id: this.province_id,
						city_id: this.city_id
					}
				}

				if (this.tabId == 4) {
					getUrl = this.$api.xiaohongshuQrcode;
					getData = {
						account_group_id: this.selGroupId,
						uid: uni.getStorageSync("uid")
					}
				}

				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					setTimeout(() => {
						if (this.tabId == 1 || this.tabId == 4) {
							this.qrCode = 'data:image/png;base64,' + result.data.qrcode;
						} else {
							this.qrCode = result.data.qrcode;
						}
						if (this.tabId == 1 || this.tabId == 3) {
							this.countDown();
						}
						if (this.tabId != 2) {
							this.polling();
						}
						this.id = result.data.id;
						this.isWhether = true;
						this.$refs.pop4.close();
						this.getQrcodeOpen();
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//选择分组
			getGroupOpen() {
				this.$refs.pop4.show({
					style: 'background-color:#fff;width:750rpx;height:auto;border-radius: 20px 20px 0 0;padding: 0 20rpx',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			getGroupClose() {
				this.$refs.pop4.close();
				this.selGroupId = ''; //分组ID
				this.selGroupName = '';
				this.cookie = '';
				this.getBatch();
			},

			//账户分组列表
			bindPickerChange4(e) {
				this.selGroupId = this.groupList[e.detail.value].id;
				this.selGroupName = this.groupList[e.detail.value].name;
			},

			//选择账户类型
			setTabId(id) {
				if (id == 1 || id == 3) {
					this.getCity();
				}
				this.tabId = id;
				this.groupType = id;
				this.getAccountGroupList();
				this.$refs.pop2.close();
				this.getGroupOpen();
			},

			//账户
			getBatch() {
				this.$refs.pop2.show({
					style: 'background-color:#fff;width:750rpx;height:auto;border-radius: 20px 20px 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			closeBatch() {
				this.$refs.pop2.close();
				uni.redirectTo({
					url: '/pages/matrix/account?tabId=' + this.tabId
				})
			},

			//添加分组
			getAddGroup() {
				this.$refs.pop4.close();
				this.$refs.pop3.show({
					style: 'background-color:#fff;width:750rpx;height:auto;border-radius: 20px 20px 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			getAddGroupClose() {
				this.$refs.pop3.close();
				this.getGroupOpen();
			},

			//分组类型
			getType(id) {
				this.groupType = id;
			},

			//添加分组
			async getAdd() {

				if (!this.groupName) {
					this.$sun.toast("请输入分组名称", 'none');
					return;
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.addAccountGroup,
					data: {
						name: this.groupName,
						type: this.groupType,
						uid: uni.getStorageSync("uid"),
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.groupName = '';
						this.getAddGroupClose();
						this.isWhether = true;
						this.getAccountGroupList();
						this.getGroupOpen();
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//分组列表
			async getAccountGroupList() {
				const result = await this.$http.post({
					url: this.$api.accountGroupList,
					data: {
						type: this.tabId,
						uid: uni.getStorageSync("uid"),
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {
					this.groupList = result.data.list;
				}
			},


		}
	}
</script>

<style lang="scss">
	.img-374 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.get-date {
		width: 60rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		background-color: #69F8AA;
		color: #000;
		margin-right: 10rpx;
	}

	.img-qr {
		width: 508rpx;
		height: 508rpx;
	}

	.qr-code-bor {
		width: 556rpx;
		height: 556rpx;
		border: 1px solid #E9E9E9;
		margin: 0 97rpx 32rpx;
		padding: 24rpx;
	}

	.bg {
		width: 750rpx;
		height: 200rpx;
		border: 40rpx 40rpx 0 0;
		background-repeat: no-repeat;
		background-size: cover;
		text-align: center;
		padding: 54rpx 0 40rpx;
	}

	.line-au {
		width: 2rpx;
		height: 70rpx;
		background-color: #F7F7F7;
	}

	.matrix-20 {
		width: 24rpx;
		height: 24rpx;
		margin-left: auto;
	}

	.list-but {
		width: 600rpx;
		background-color: #0E8FF3;
		padding: 24rpx;
		font-size: 30rpx;
		font-weight: bold;
		text-align: center;
		color: #FFFFFF;
		margin-left: 42rpx;
		border-radius: 10rpx;
	}

	.input-name {
		width: 640rpx;
		// border: 1px solid #EBEBEB;
		background-color: #F4F3F3;
		border-radius: 10rpx;
		padding: 30rpx 20rpx;
		margin-bottom: 40rpx;
	}

	.matrix-12 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}

	.cancel {
		width: 750prx;
		text-align: center;
		padding: 40rpx 0;
		font-size: 32rpx;
	}

	.matrix-17 {
		width: 96rpx;
		height: 96rpx;
		margin-bottom: 20rpx;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}
</style>