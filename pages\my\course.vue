<template>
	<view>

		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">

			<view class="display-fw-js" style="padding: 0 20rpx">
				<block v-for="(item,index) in list" :key="index">
					<view>
						<view class="r-frame-w">
							<video class="introduce" :src="item.introduce_url"></video>
						</view>
						<view class="frame-title margin-bottom_30rpx">
							<view class="display-a-js">
								<view class="font-size_26rpx font-overflow2 color_FFFFFF">
									{{item.title}}
								</view>

							</view>
						</view>
					</view>

				</block>
			</view>

		</mescroll-body>

	</view>
</template>

<script>
	export default {
		data() {
			return {

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

				windowHeight: '',

				imgUrl: this.$imgUrl,

			}
		},

		onLoad() {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 20;
				},
			})
			this.$nextTick(() => {
				this.mescroll.resetUpScroll();
			});
		},

		onShow() {

		},

		methods: {

			// detail(obj) {
			// 	uni.navigateTo({
			// 		url: '/pages/my/courseDetail?id='+obj.id
			// 	})
			// },

			//帮助教程
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.getHelpTutoria,
					data: {
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},

		}
	}
</script>

<style lang="scss">
	.introduce {
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx 10rpx 0 0;
	}

	.frame-title {
		background-color: #414141;
		padding: 20rpx 10rpx;
		width: 344rpx;
		border-radius: 0 0 10rpx 10rpx;

	}

	.r-frame-w {
		width: 344rpx;
		border-radius: 10rpx 10rpx 0 0;
		position: relative;
		height: 612rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: block;
		/* 避免底部空白 */
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
</style>