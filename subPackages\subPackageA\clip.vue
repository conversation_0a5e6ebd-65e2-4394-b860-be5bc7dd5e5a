<template>
	<view class="main">
		<!-- 自定义导航栏 -->
		<view class="bg" :style="{'height':heightSystemss + statusBarHeightss +'px'}">
			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					{{ step === 1 ? '语音识别结果编辑' : '选择模版' }}
				</view>
			</view>
		</view>

		<!-- 修改字幕 -->
		<template v-if="step === 1">
			<!-- 语音识别结果列表 -->
			<view class="c-content">
				<view class="utterance-list">
					<view v-for="(utterance, index) in utterances" :key="index" class="utterance-item">
						<view class="utterance-content">
							<view class="utterance-main">
								<text class="utterance-time">{{ getTimeRange(utterance) }}</text>
								<text class="utterance-text">{{ utterance.text }}</text>
							</view>
							<view class="action-buttons">
								<button class="edit-btn" @click="openEditModal(index)">
									修改
								</button>
								<button class="delete-btn" @click="deleteUtterance(index)">
									删除
								</button>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="footer">
				<view class="footer-buttons">
					<button class="reset-btn" @click="resetData">重置</button>
					<button class="next-btn" @click="handleNext">下一步</button>
				</view>
			</view>

			<!-- 编辑弹窗 -->
			<view v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
				<view class="modal-content" @click.stop>
					<view class="modal-header">
						<text class="modal-title">编辑文字</text>
						<text class="close-btn" @click="closeEditModal">×</text>
					</view>

					<view class="modal-body">
						<view class="text-edit-container">
							<!-- 原始文本显示区域 -->
							<view class="original-text-section">
								<view class="section-label">
									<text class="label-text">原始文本：</text>
								</view>
								<view class="original-text-display">
									<text class="original-text">{{ originalText }}</text>
								</view>
							</view>

							<!-- 编辑文本区域 -->
							<view class="edit-text-section">
								<view class="section-label">
									<text class="label-text">修改文本：</text>
									<text class="char-count">{{ currentEditText.length }}/{{ maxTextLength }}</text>
								</view>
								<textarea
									class="text-input"
									:value="currentEditText"
									@input="updateText"
									:maxlength="maxTextLength"
									placeholder="请输入字幕内容..."
									auto-height
									:show-confirm-bar="false"
								></textarea>
							</view>
						</view>
					</view>

					<view class="modal-footer">
						<button class="confirm-btn" @click="confirmEdit">确认修改</button>
						<button class="cancel-btn" @click="closeEditModal">取消</button>
					</view>
				</view>
			</view>
		</template>
		<!-- 选择模板 -->
		<template v-else-if="step === 2">
			<view class="template-page">
				<!-- 顶部视频预览区域 -->
				<view class="video-preview-section">
					<video class="video-player" :src="videoData.result" :poster="videoData.result_cover" controls
						show-center-play-btn show-play-btn object-fit="contain" @error="onVideoError"
						@play="onVideoPlay"></video>
				</view>

				<!-- 中间一级菜单区域 -->
				<view class="menu-section">
					<scroll-view class="menu-scroll" scroll-x="true" show-scrollbar="false">
						<view class="menu-list">
							<view v-for="(menu, index) in menuItems" :key="menu.id" class="menu-item"
								:class="{ active: selectedMenuIndex === index }" @click="selectMenu(index)">
								<text class="menu-text">{{ menu.name }}</text>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 底部模板选择区域 -->
				<view class="template-section">
					<scroll-view class="template-scroll" scroll-x="true" show-scrollbar="false">
						<view class="template-list">
							<view v-for="(template, index) in templates" :key="template.id" class="template-item"
								:class="{ active: selectedTemplateIndex === index }" @click="selectTemplate(index)">
								<image class="template-image" :src="template.image" mode="aspectFill"
									@error="onImageError"></image>
								<text class="template-name">{{ template.name }}</text>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 固定底部保存按钮 -->
				<view class="save-button-container">
					<button class="save-btn" @click="handleSave">
						<text class="save-text">保存</text>
					</button>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 示例数据 - 实际使用时从服务端获取
				utterances: [{
						"text": "苏州的家人们看过来",
						"start_time": 2040,
						"end_time": 4220,
						"words": [{
								"text": "苏",
								"start_time": 2040,
								"end_time": 2200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "州",
								"start_time": 2200,
								"end_time": 2360,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 2360,
								"end_time": 2560,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "家",
								"start_time": 2640,
								"end_time": 2880,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "人",
								"start_time": 2880,
								"end_time": 3000,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 3000,
								"end_time": 3200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "看",
								"start_time": 3280,
								"end_time": 3520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "过",
								"start_time": 3520,
								"end_time": 3720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "来",
								"start_time": 3720,
								"end_time": 4220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "我们扎根在一个相城区",
						"start_time": 4320,
						"end_time": 7040,
						"words": [{
								"text": "我",
								"start_time": 4320,
								"end_time": 4680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 4680,
								"end_time": 4960,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "扎",
								"start_time": 5000,
								"end_time": 5240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "根",
								"start_time": 5280,
								"end_time": 5480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "在",
								"start_time": 5480,
								"end_time": 5720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "一",
								"start_time": 5960,
								"end_time": 6160,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "个",
								"start_time": 6160,
								"end_time": 6400,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "相",
								"start_time": 6400,
								"end_time": 6640,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "城",
								"start_time": 6640,
								"end_time": 6840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "区",
								"start_time": 6840,
								"end_time": 7040,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "八千平米的呃",
						"start_time": 7040,
						"end_time": 8920,
						"words": [{
								"text": "八",
								"start_time": 7040,
								"end_time": 7240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "千",
								"start_time": 7240,
								"end_time": 7480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "平",
								"start_time": 7480,
								"end_time": 7720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "米",
								"start_time": 7720,
								"end_time": 7840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 7840,
								"end_time": 8080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "呃",
								"start_time": 8760,
								"end_time": 8920,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "工厂直营店",
						"start_time": 8920,
						"end_time": 10220,
						"words": [{
								"text": "工",
								"start_time": 8920,
								"end_time": 9080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "厂",
								"start_time": 9080,
								"end_time": 9320,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "直",
								"start_time": 9320,
								"end_time": 9520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "营",
								"start_time": 9520,
								"end_time": 9680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "店",
								"start_time": 9720,
								"end_time": 10220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "位置偏了点",
						"start_time": 11360,
						"end_time": 12700,
						"words": [{
								"text": "位",
								"start_time": 11360,
								"end_time": 11600,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "置",
								"start_time": 11600,
								"end_time": 11800,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "偏",
								"start_time": 11840,
								"end_time": 12040,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "了",
								"start_time": 12040,
								"end_time": 12200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "点",
								"start_time": 12200,
								"end_time": 12700,
								"attribute": {
									"event": "speech"
								}
							}
						]
					}, {
						"text": "苏州的家人们看过来",
						"start_time": 2040,
						"end_time": 4220,
						"words": [{
								"text": "苏",
								"start_time": 2040,
								"end_time": 2200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "州",
								"start_time": 2200,
								"end_time": 2360,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 2360,
								"end_time": 2560,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "家",
								"start_time": 2640,
								"end_time": 2880,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "人",
								"start_time": 2880,
								"end_time": 3000,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 3000,
								"end_time": 3200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "看",
								"start_time": 3280,
								"end_time": 3520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "过",
								"start_time": 3520,
								"end_time": 3720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "来",
								"start_time": 3720,
								"end_time": 4220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "我们扎根在一个相城区",
						"start_time": 4320,
						"end_time": 7040,
						"words": [{
								"text": "我",
								"start_time": 4320,
								"end_time": 4680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 4680,
								"end_time": 4960,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "扎",
								"start_time": 5000,
								"end_time": 5240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "根",
								"start_time": 5280,
								"end_time": 5480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "在",
								"start_time": 5480,
								"end_time": 5720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "一",
								"start_time": 5960,
								"end_time": 6160,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "个",
								"start_time": 6160,
								"end_time": 6400,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "相",
								"start_time": 6400,
								"end_time": 6640,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "城",
								"start_time": 6640,
								"end_time": 6840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "区",
								"start_time": 6840,
								"end_time": 7040,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "八千平米的呃",
						"start_time": 7040,
						"end_time": 8920,
						"words": [{
								"text": "八",
								"start_time": 7040,
								"end_time": 7240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "千",
								"start_time": 7240,
								"end_time": 7480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "平",
								"start_time": 7480,
								"end_time": 7720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "米",
								"start_time": 7720,
								"end_time": 7840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 7840,
								"end_time": 8080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "呃",
								"start_time": 8760,
								"end_time": 8920,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "工厂直营店",
						"start_time": 8920,
						"end_time": 10220,
						"words": [{
								"text": "工",
								"start_time": 8920,
								"end_time": 9080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "厂",
								"start_time": 9080,
								"end_time": 9320,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "直",
								"start_time": 9320,
								"end_time": 9520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "营",
								"start_time": 9520,
								"end_time": 9680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "店",
								"start_time": 9720,
								"end_time": 10220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "位置偏了点",
						"start_time": 11360,
						"end_time": 12700,
						"words": [{
								"text": "位",
								"start_time": 11360,
								"end_time": 11600,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "置",
								"start_time": 11600,
								"end_time": 11800,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "偏",
								"start_time": 11840,
								"end_time": 12040,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "了",
								"start_time": 12040,
								"end_time": 12200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "点",
								"start_time": 12200,
								"end_time": 12700,
								"attribute": {
									"event": "speech"
								}
							}
						]
					}, {
						"text": "苏州的家人们看过来",
						"start_time": 2040,
						"end_time": 4220,
						"words": [{
								"text": "苏",
								"start_time": 2040,
								"end_time": 2200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "州",
								"start_time": 2200,
								"end_time": 2360,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 2360,
								"end_time": 2560,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "家",
								"start_time": 2640,
								"end_time": 2880,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "人",
								"start_time": 2880,
								"end_time": 3000,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 3000,
								"end_time": 3200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "看",
								"start_time": 3280,
								"end_time": 3520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "过",
								"start_time": 3520,
								"end_time": 3720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "来",
								"start_time": 3720,
								"end_time": 4220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "我们扎根在一个相城区",
						"start_time": 4320,
						"end_time": 7040,
						"words": [{
								"text": "我",
								"start_time": 4320,
								"end_time": 4680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 4680,
								"end_time": 4960,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "扎",
								"start_time": 5000,
								"end_time": 5240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "根",
								"start_time": 5280,
								"end_time": 5480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "在",
								"start_time": 5480,
								"end_time": 5720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "一",
								"start_time": 5960,
								"end_time": 6160,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "个",
								"start_time": 6160,
								"end_time": 6400,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "相",
								"start_time": 6400,
								"end_time": 6640,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "城",
								"start_time": 6640,
								"end_time": 6840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "区",
								"start_time": 6840,
								"end_time": 7040,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "八千平米的呃",
						"start_time": 7040,
						"end_time": 8920,
						"words": [{
								"text": "八",
								"start_time": 7040,
								"end_time": 7240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "千",
								"start_time": 7240,
								"end_time": 7480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "平",
								"start_time": 7480,
								"end_time": 7720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "米",
								"start_time": 7720,
								"end_time": 7840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 7840,
								"end_time": 8080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "呃",
								"start_time": 8760,
								"end_time": 8920,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "工厂直营店",
						"start_time": 8920,
						"end_time": 10220,
						"words": [{
								"text": "工",
								"start_time": 8920,
								"end_time": 9080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "厂",
								"start_time": 9080,
								"end_time": 9320,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "直",
								"start_time": 9320,
								"end_time": 9520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "营",
								"start_time": 9520,
								"end_time": 9680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "店",
								"start_time": 9720,
								"end_time": 10220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "位置偏了点",
						"start_time": 11360,
						"end_time": 12700,
						"words": [{
								"text": "位",
								"start_time": 11360,
								"end_time": 11600,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "置",
								"start_time": 11600,
								"end_time": 11800,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "偏",
								"start_time": 11840,
								"end_time": 12040,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "了",
								"start_time": 12040,
								"end_time": 12200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "点",
								"start_time": 12200,
								"end_time": 12700,
								"attribute": {
									"event": "speech"
								}
							}
						]
					}, {
						"text": "苏州的家人们看过来",
						"start_time": 2040,
						"end_time": 4220,
						"words": [{
								"text": "苏",
								"start_time": 2040,
								"end_time": 2200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "州",
								"start_time": 2200,
								"end_time": 2360,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 2360,
								"end_time": 2560,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "家",
								"start_time": 2640,
								"end_time": 2880,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "人",
								"start_time": 2880,
								"end_time": 3000,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 3000,
								"end_time": 3200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "看",
								"start_time": 3280,
								"end_time": 3520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "过",
								"start_time": 3520,
								"end_time": 3720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "来",
								"start_time": 3720,
								"end_time": 4220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "我们扎根在一个相城区",
						"start_time": 4320,
						"end_time": 7040,
						"words": [{
								"text": "我",
								"start_time": 4320,
								"end_time": 4680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 4680,
								"end_time": 4960,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "扎",
								"start_time": 5000,
								"end_time": 5240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "根",
								"start_time": 5280,
								"end_time": 5480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "在",
								"start_time": 5480,
								"end_time": 5720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "一",
								"start_time": 5960,
								"end_time": 6160,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "个",
								"start_time": 6160,
								"end_time": 6400,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "相",
								"start_time": 6400,
								"end_time": 6640,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "城",
								"start_time": 6640,
								"end_time": 6840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "区",
								"start_time": 6840,
								"end_time": 7040,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "八千平米的呃",
						"start_time": 7040,
						"end_time": 8920,
						"words": [{
								"text": "八",
								"start_time": 7040,
								"end_time": 7240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "千",
								"start_time": 7240,
								"end_time": 7480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "平",
								"start_time": 7480,
								"end_time": 7720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "米",
								"start_time": 7720,
								"end_time": 7840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 7840,
								"end_time": 8080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "呃",
								"start_time": 8760,
								"end_time": 8920,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "工厂直营店",
						"start_time": 8920,
						"end_time": 10220,
						"words": [{
								"text": "工",
								"start_time": 8920,
								"end_time": 9080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "厂",
								"start_time": 9080,
								"end_time": 9320,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "直",
								"start_time": 9320,
								"end_time": 9520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "营",
								"start_time": 9520,
								"end_time": 9680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "店",
								"start_time": 9720,
								"end_time": 10220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "位置偏了点",
						"start_time": 11360,
						"end_time": 12700,
						"words": [{
								"text": "位",
								"start_time": 11360,
								"end_time": 11600,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "置",
								"start_time": 11600,
								"end_time": 11800,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "偏",
								"start_time": 11840,
								"end_time": 12040,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "了",
								"start_time": 12040,
								"end_time": 12200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "点",
								"start_time": 12200,
								"end_time": 12700,
								"attribute": {
									"event": "speech"
								}
							}
						]
					}
				],
				// 原始数据副本，用于重置
				originalUtterances: [],
				// 弹窗相关状态
				showEditModal: false,
				currentEditIndex: -1,
				currentEditWords: [],
				// 新增：文本编辑相关
				currentEditText: '', // 当前编辑的完整文本
				maxTextLength: 0, // 最大文本长度限制
				originalText: '', // 原始文本内容（来自originalUtterances）
				originalWords: [], // 原始words数组（来自originalUtterances）
				// 步骤 1修改字幕 2选择模版
				step: 1,

				// 自定义导航栏相关
				heightSystemss: '',
				statusBarHeightss: '',
				imgUrl: this.$imgUrl,

				videoData: {
					// id
					id: null,
					// 封面图片
					result_cover: '',
					// 视频链接
					result: ''
				},
				// 模板选择相关
				selectedTemplateIndex: 0,
				selectedMenuIndex: 0,
				// 一级菜单数据
				menuItems: [{
						id: 1,
						name: '滤镜',
						type: 'filter'
					},
					{
						id: 2,
						name: '特效',
						type: 'effect'
					},
					{
						id: 3,
						name: '音乐',
						type: 'music'
					},
					{
						id: 4,
						name: '字幕',
						type: 'subtitle'
					},
					{
						id: 5,
						name: '贴纸',
						type: 'sticker'
					},
					{
						id: 6,
						name: '转场',
						type: 'transition'
					},
					{
						id: 7,
						name: '调色',
						type: 'color'
					},
					{
						id: 8,
						name: '速度',
						type: 'speed'
					}
				],
				// 模板数据
				templates: [{
						id: 1,
						name: '背景音乐',
						type: 'music',
						image: 'https://aiszr-jiajs.oss-cn-beijing.aliyuncs.com/32142.mp4?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'
					},
					{
						id: 2,
						name: '模板2',
						type: 'template',
						image: 'https://aiszr-jiajs.oss-cn-beijing.aliyuncs.com/32142.mp4?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'
					},
					{
						id: 3,
						name: '模板3',
						type: 'template',
						image: 'https://aiszr-jiajs.oss-cn-beijing.aliyuncs.com/32142.mp4?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'
					},
					{
						id: 4,
						name: '模板4',
						type: 'template',
						image: 'https://aiszr-jiajs.oss-cn-beijing.aliyuncs.com/32142.mp4?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'
					},
					{
						id: 5,
						name: '模板5',
						type: 'template',
						image: 'https://aiszr-jiajs.oss-cn-beijing.aliyuncs.com/32142.mp4?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'
					}
				]
			}
		},
		methods: {
			// 将毫秒转换为MM:SS格式
			formatTime(milliseconds) {
				if (!milliseconds && milliseconds !== 0) {
					return '00:00';
				}

				const totalSeconds = Math.floor(milliseconds / 1000);
				const minutes = Math.floor(totalSeconds / 60);
				const seconds = totalSeconds % 60;

				// 格式化为两位数
				const formattedMinutes = minutes.toString().padStart(2, '0');
				const formattedSeconds = seconds.toString().padStart(2, '0');

				return `${formattedMinutes}:${formattedSeconds}`;
			},

			// 获取时间范围显示文本
			getTimeRange(utterance) {
				const startTime = this.formatTime(utterance.start_time);
				const endTime = this.formatTime(utterance.end_time);
				return `${startTime} - ${endTime}`;
			},

			// 获取原始文本内容
			getOriginalText(currentUtterance) {
				// 通过时间范围匹配找到对应的原始数据
				if (!this.originalUtterances || this.originalUtterances.length === 0) {
					return currentUtterance.text; // 如果没有原始数据，返回当前文本
				}

				const currentStartTime = currentUtterance.start_time;
				const currentEndTime = currentUtterance.end_time;

				// 查找时间范围匹配的原始数据
				const originalUtterance = this.originalUtterances.find(original =>
					original.start_time === currentStartTime && original.end_time === currentEndTime
				);

				if (originalUtterance) {
					// 如果找到匹配的原始数据，返回原始文本
					return originalUtterance.words.map(word => word.text).join('');
				} else {
					// 如果没有找到精确匹配，尝试模糊匹配（时间范围重叠）
					const fuzzyMatch = this.originalUtterances.find(original =>
						(currentStartTime >= original.start_time && currentStartTime <= original.end_time) ||
						(currentEndTime >= original.start_time && currentEndTime <= original.end_time) ||
						(currentStartTime <= original.start_time && currentEndTime >= original.end_time)
					);

					if (fuzzyMatch) {
						return fuzzyMatch.words.map(word => word.text).join('');
					}
				}

				// 如果都没有找到，返回当前文本
				return currentUtterance.text;
			},

			// 获取原始words数组
			getOriginalWords(currentUtterance) {
				// 通过时间范围匹配找到对应的原始words数组
				if (!this.originalUtterances || this.originalUtterances.length === 0) {
					return currentUtterance.words; // 如果没有原始数据，返回当前words
				}

				const currentStartTime = currentUtterance.start_time;
				const currentEndTime = currentUtterance.end_time;

				// 查找时间范围匹配的原始数据
				const originalUtterance = this.originalUtterances.find(original =>
					original.start_time === currentStartTime && original.end_time === currentEndTime
				);

				if (originalUtterance) {
					// 如果找到匹配的原始数据，返回原始words数组
					return originalUtterance.words;
				} else {
					// 如果没有找到精确匹配，尝试模糊匹配（时间范围重叠）
					const fuzzyMatch = this.originalUtterances.find(original =>
						(currentStartTime >= original.start_time && currentStartTime <= original.end_time) ||
						(currentEndTime >= original.start_time && currentEndTime <= original.end_time) ||
						(currentStartTime <= original.start_time && currentEndTime >= original.end_time)
					);

					if (fuzzyMatch) {
						return fuzzyMatch.words;
					}
				}

				// 如果都没有找到，返回当前words
				return currentUtterance.words;
			},

			// 打开编辑弹窗
			openEditModal(index) {
				if (index < 0 || index >= this.utterances.length) {
					uni.showToast({
						title: '无效的索引',
						icon: 'error'
					});
					return;
				}

				this.currentEditIndex = index;
				this.currentEditWords = JSON.parse(JSON.stringify(this.utterances[index].words));

				// 获取原始文本内容和原始words数组
				this.originalText = this.getOriginalText(this.utterances[index]);
				this.originalWords = JSON.parse(JSON.stringify(this.getOriginalWords(this.utterances[index])));

				// 计算当前编辑文本内容，最大长度基于原始words数组
				this.currentEditText = this.currentEditWords.map(word => word.text).join('');
				this.maxTextLength = this.originalWords.length; // 使用原始words数组的长度

				this.showEditModal = true;
			},

			// 关闭编辑弹窗
			closeEditModal() {
				this.showEditModal = false;
				this.currentEditIndex = -1;
				this.currentEditWords = [];
				this.currentEditText = '';
				this.maxTextLength = 0;
				this.originalText = '';
				this.originalWords = [];
			},

			// 更新文本内容
			updateText(event) {
				const newText = event.detail.value;

				// 检查长度限制
				if (newText.length <= this.maxTextLength) {
					this.currentEditText = newText;
				} else {
					// 如果超过长度限制，截取到最大长度
					this.currentEditText = newText.substring(0, this.maxTextLength);

					// 提示用户
					uni.showToast({
						title: `最多只能输入${this.maxTextLength}个字符`,
						icon: 'none',
						duration: 1500
					});
				}
			},

			// 验证文本输入
			validateTextInput(text) {
				// 检查是否为空
				if (!text || text.trim() === '') {
					return {
						valid: false,
						message: '文本内容不能为空'
					};
				}

				// 检查长度限制
				if (text.length > this.maxTextLength) {
					return {
						valid: false,
						message: `文本长度不能超过${this.maxTextLength}个字符`
					};
				}

				return {
					valid: true,
					message: ''
				};
			},

			// 确认修改
			confirmEdit() {
				if (this.currentEditIndex >= 0) {
					try {
						// 验证数据完整性
						if (!this.currentEditWords || this.currentEditWords.length === 0) {
							uni.showToast({
								title: '没有可修改的数据',
								icon: 'error'
							});
							return;
						}

						// 验证文本内容
						const validation = this.validateTextInput(this.currentEditText);
						if (!validation.valid) {
							uni.showToast({
								title: validation.message,
								icon: 'error'
							});
							return;
						}

						// 检查长度变化并显示警告
						if (this.currentEditText.length < this.originalText.length) {
							uni.showModal({
								title: '长度变化警告',
								content: '修改后的文本长度小于原始长度，可能会造成视频和字幕不一致，是否确认修改？',
								success: (res) => {
									if (res.confirm) {
										// 用户确认修改，执行修改逻辑
										this.executeTextModification();
									}
									// 用户取消则不执行任何操作，保持编辑状态
								}
							});
							return;
						}

						// 如果长度没有减少，直接执行修改
						this.executeTextModification();
					} catch (error) {
						console.error('修改失败：', error);
						uni.showToast({
							title: '修改失败',
							icon: 'error'
						});
					}
				}
			},

			// 执行文本修改的具体逻辑
			executeTextModification() {
				try {
					// 将新文本重新分配到words数组，使用原始words数组的时间和属性信息
					const newTextArray = this.currentEditText.split('');
					const originalWordsArray = this.originalWords; // 使用原始words数组
					const newWords = [];

					// 处理文本分配逻辑
					for (let i = 0; i < newTextArray.length; i++) {
						if (i < originalWordsArray.length) {
							// 使用原始words的时间和属性信息，只更新文本
							newWords.push({
								text: newTextArray[i],
								start_time: originalWordsArray[i].start_time,
								end_time: originalWordsArray[i].end_time,
								attribute: originalWordsArray[i].attribute
							});
						} else {
							// 如果新文本长度超过原始数组，复制最后一个原始元素的时间和属性
							const lastOriginalWord = originalWordsArray[originalWordsArray.length - 1];
							newWords.push({
								text: newTextArray[i],
								start_time: lastOriginalWord.start_time,
								end_time: lastOriginalWord.end_time,
								attribute: lastOriginalWord.attribute
							});
						}
					}

					// 更新数据
					this.utterances[this.currentEditIndex].words = newWords;
					this.utterances[this.currentEditIndex].text = this.currentEditText;

					// 更新父级的end_time为words最后一个元素的end_time
					if (newWords.length > 0) {
						this.utterances[this.currentEditIndex].end_time = newWords[newWords.length - 1].end_time;
					}

					uni.showToast({
						title: '修改成功',
						icon: 'success'
					});

					this.closeEditModal();
				} catch (error) {
					console.error('修改失败：', error);
					uni.showToast({
						title: '修改失败',
						icon: 'error'
					});
				}
			},

			// 下一步操作
			handleNext() {
				try {
					// 验证数据完整性
					if (!this.utterances || this.utterances.length === 0) {
						uni.showToast({
							title: '没有可输出的数据',
							icon: 'error'
						});
						return;
					}

					// 清理空数据并重新构建数据
					const cleanedUtterances = this.utterances.map(utterance => {
						// 过滤掉空字符的 words
						const filteredWords = utterance.words.filter(word => word.text && word.text.trim() !== '');

						// 重新拼接 text 字段
						const newText = filteredWords.map(word => word.text).join('');

						return {
							...utterance,
							text: newText,
							words: filteredWords
						};
					}).filter(utterance => utterance.text && utterance.text.trim() !== ''); // 过滤掉完全为空的 utterance

					// 更新当前数据
					this.utterances = cleanedUtterances;

					// 输出完整数据
					const outputData = {
						utterances: cleanedUtterances,
						timestamp: new Date().toISOString(),
						totalCount: cleanedUtterances.length,
						cleanupInfo: {
							message: '已自动清理空字符数据'
						}
					};

					console.log('修改后的完整数据：', outputData);

					// 可以在这里添加数据上传或保存逻辑
					// this.uploadData(outputData);

					uni.showModal({
						title: '数据输出成功',
						content: `已输出 ${cleanedUtterances.length} 条语音识别结果到控制台（已自动清理空数据）`,
						showCancel: false,
						confirmText: '确定'
					});

					this.step++

				} catch (error) {
					console.error('输出数据失败：', error);
					uni.showToast({
						title: '输出失败',
						icon: 'error'
					});
				}
			},

			// 删除语音识别项
			deleteUtterance(index) {
				uni.showModal({
					title: '确认删除',
					content: `是否要删除这条语音识别结果："${this.utterances[index].text}"？`,
					success: (res) => {
						if (res.confirm) {
							this.utterances.splice(index, 1);
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},

			// 重置数据
			resetData() {
				uni.showModal({
					title: '确认重置',
					content: '是否要重置所有修改？这将恢复到原始数据状态。',
					success: (res) => {
						if (res.confirm) {
							// 使用原始数据副本恢复
							this.utterances = JSON.parse(JSON.stringify(this.originalUtterances));
							uni.showToast({
								title: '已重置',
								icon: 'success'
							});
						}
					}
				});
			},

			// 初始化原始数据副本
			initializeOriginalData() {
				this.originalUtterances = JSON.parse(JSON.stringify(this.utterances));
			},

			// 选择菜单
			selectMenu(index) {
				this.selectedMenuIndex = index;
				const selectedMenu = this.menuItems[index];
				console.log('选择菜单：', selectedMenu);

				uni.showToast({
					title: `切换到：${selectedMenu.name}`,
					icon: 'success'
				});
			},

			// 选择模板
			selectTemplate(index) {
				this.selectedTemplateIndex = index;
				const selectedTemplate = this.templates[index];
				console.log('选择模板：', selectedTemplate);

				uni.showToast({
					title: `已选择：${selectedTemplate.name}`,
					icon: 'success'
				});
			},

			// 视频错误处理
			onVideoError(e) {
				console.error('视频加载错误：', e);
				uni.showToast({
					title: '视频加载失败',
					icon: 'error'
				});
			},

			// 视频播放事件
			onVideoPlay(e) {
				console.log('视频开始播放：', e);
			},

			// 图片错误处理
			onImageError(e) {
				console.error('图片加载错误：', e);
			},

			// 保存模板选择
			handleSave() {
				const selectedTemplate = this.templates[this.selectedTemplateIndex];

				uni.showModal({
					title: '保存成功',
					content: `已保存模板：${selectedTemplate.name}`,
					showCancel: false,
					confirmText: '确定',
					success: () => {
						// 这里可以添加保存逻辑
						console.log('保存模板选择', {
							selectedTemplate: selectedTemplate,
							videoData: this.videoData,
							utterances: this.utterances,
							timestamp: new Date().toISOString()
						});
					}
				});
			},

			// 获取系统信息
			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

			// 自定义导航栏返回
			navig() {
				if (this.step > 1) {
					// 如果step大于1，返回上一步
					this.step--;
				} else {
					// 如果step等于1，返回页面
					let pages = getCurrentPages(); //获取所有页面栈实例列表
					if (pages.length == 1) {
						uni.reLaunch({
							url: '/pages/index/index'
						})
					} else {
						uni.navigateBack();
					}
				}
			}
		},
		onLoad() {
			// 如果有存储的数据，可以在这里加载
			this.videoData = uni.getStorageSync('clipData') ? uni.getStorageSync('clipData')[0] : this.videoData

			// 初始化原始数据副本
			this.initializeOriginalData();

			// 初始化自定义导航栏
			this.getSystemInfo();

			console.log('页面加载完成，当前数据：', this.utterances);
		}
	}
</script>

<style lang="scss">
	page {
		width: 100%;
		overflow-x: hidden;
		background-color: #000;
		border-top: none !important;
	}

	/* 自定义导航栏样式 */
	.bg {
		width: 100%;
		background-size: 100% 100%;
		position: relative;
	}

	.iconDizhssi {
		position: absolute;
		display: flex;
		align-items: center;
		color: #fff;
		z-index: 999;

		.img-34 {
			width: 34rpx;
			height: 34rpx;
			margin-right: 20rpx;
		}

		.font-size_30rpx {
			font-size: 30rpx;
			color: #fff;
		}
	}

	.main {
		min-height: 100vh;
		background-color: #000;
		color: #fff;
		display: flex;
		flex-direction: column;
	}

	/* 内容区域 */
	.c-content {
		flex: 1;
		padding: 30rpx;
		padding-bottom: 200rpx;
	}

	/* 语音识别结果列表 */
	.utterance-list {
		.utterance-item {
			margin-bottom: 20rpx;
			background-color: #111;
			border-radius: 12rpx;
			border-bottom: 1px solid #333;
			overflow: hidden;

			.utterance-content {
				padding-bottom: 20rpx;
				display: flex;
				align-items: flex-start;
				justify-content: space-between;

				.utterance-main {
					flex: 1;
					margin-right: 20rpx;

					.utterance-time {
						display: block;
						font-size: 24rpx;
						color: #999;
						margin-bottom: 8rpx;
						line-height: 1.2;
					}

					.utterance-text {
						display: block;
						font-size: 32rpx;
						line-height: 1.6;
						color: #fff;
						word-break: break-all;
					}
				}

				.action-buttons {
					display: flex;
					gap: 15rpx;

					.edit-btn {
						background: linear-gradient(90deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
						color: #fff;
						border: none;
						border-radius: 8rpx;

						font-size: 28rpx;
						min-width: 100rpx;

						&:active {
							opacity: 0.8;
						}
					}

					.delete-btn {
						background: linear-gradient(90deg, #FF3B30, #FF6B47 100%);
						color: #fff;
						border: none;
						border-radius: 8rpx;

						font-size: 28rpx;
						min-width: 100rpx;

						&:active {
							opacity: 0.8;
						}
					}
				}
			}
		}
	}

	/* 底部操作区域 */
	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 30rpx;
		border-top: 1px solid #333;
		background-color: #000;

		.footer-buttons {
			display: flex;
			gap: 20rpx;

			.reset-btn {
				flex: 1;
				background: linear-gradient(90deg, #FF3B30, #FF6B47 100%);
				color: #fff;
				border: none;
				border-radius: 12rpx;
				font-size: 32rpx;
				font-weight: bold;

				&:active {
					opacity: 0.8;
				}
			}

			.next-btn {
				flex: 2;
				background: linear-gradient(90deg, rgba(106, 249, 167, 1), rgba(90, 232, 233, 1) 100%);
				color: #fff;
				border: none;
				border-radius: 12rpx;
				font-size: 32rpx;
				font-weight: bold;

				&:active {
					opacity: 0.8;
				}
			}
		}
	}

	/* 弹窗遮罩 */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 60rpx;
	}

	/* 弹窗内容 */
	.modal-content {
		background-color: #111;
		border-radius: 16rpx;
		border: 1px solid #333;
		width: 100%;
		max-width: 600rpx;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
	}

	/* 弹窗头部 */
	.modal-header {
		padding: 30rpx;
		border-bottom: 1px solid #333;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.modal-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #fff;
		}

		.close-btn {
			font-size: 48rpx;
			color: #999;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			&:active {
				color: #fff;
			}
		}
	}

	/* 弹窗主体 */
	.modal-body {
		flex: 1;
		padding: 30rpx;
		overflow-y: auto;

		.text-edit-container {
			/* 原始文本显示区域 */
			.original-text-section {
				margin-bottom: 30rpx;

				.section-label {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 15rpx;

					.label-text {
						font-size: 28rpx;
						color: #fff;
						font-weight: bold;
					}
				}

				.original-text-display {
					background-color: #1a1a1a;
					border: 2rpx solid #333;
					border-radius: 8rpx;
					padding: 20rpx;
					min-height: 100rpx;

					.original-text {
						font-size: 28rpx;
						color: #ccc;
						line-height: 1.5;
						word-break: break-all;
					}
				}
			}

			/* 编辑文本区域 */
			.edit-text-section {
				.section-label {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 15rpx;

					.label-text {
						font-size: 28rpx;
						color: #fff;
						font-weight: bold;
					}

					.char-count {
						font-size: 24rpx;
						color: #999;
					}
				}

				.text-input {
					width: 100%;
					min-height: 150rpx;
					max-height: 300rpx;
					background-color: #222;
					border: 2rpx solid #444;
					border-radius: 8rpx;
					color: #fff;
					font-size: 28rpx;
					padding: 20rpx;
					line-height: 1.5;
					box-sizing: border-box;

					&:focus {
						border-color: #007AFF;
						background-color: #333;
					}
				}
			}
		}
	}

	/* 弹窗底部 */
	.modal-footer {
		padding: 30rpx;
		border-top: 1px solid #333;
		display: flex;
		gap: 20rpx;

		.confirm-btn {
			flex: 1;
			background: linear-gradient(90deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
			color: #fff;
			border: none;
			border-radius: 8rpx;

			font-size: 30rpx;

			&:active {
				opacity: 0.8;
			}
		}

		.cancel-btn {
			flex: 1;
			background: linear-gradient(90deg, #666, #888 100%);
			color: #fff;
			border: none;
			border-radius: 8rpx;

			font-size: 30rpx;

			&:active {
				opacity: 0.8;
			}
		}
	}

	/* 模板选择页面样式 */
	.template-page {
		min-height: 100vh;
		background-color: #000;
		display: flex;
		flex-direction: column;
		padding-bottom: 120rpx;
		/* 为固定按钮留出空间 */
	}

	/* 顶部视频预览区域 - 占据50%高度 */
	.video-preview-section {
		height: 50vh;
		padding: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.video-player {
			width: 100%;
			height: 100%;
			border-radius: 12rpx;
			background-color: #111;
		}
	}

	/* 中间一级菜单区域 */
	.menu-section {
		height: 100rpx;
		background-color: #111;
		border-top: 1px solid #333;
		border-bottom: 1px solid #333;
		display: flex;
		align-items: center;

		.menu-scroll {
			width: 100%;
			height: 100%;

			.menu-list {
				display: flex;
				align-items: center;
				height: 100%;
				padding: 0 20rpx;
				gap: 30rpx;

				.menu-item {
					flex-shrink: 0;
					padding: 10rpx 30rpx;
					border-radius: 4rpx;
					background-color: #222;
					transition: all 0.3s ease;

					.menu-text {
						font-size: 28rpx;
						color: #fff;
						white-space: nowrap;
					}

					&.active {
						background: linear-gradient(90deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);

						.menu-text {
							color: #fff;
							font-weight: bold;
						}
					}

					&:active {
						transform: scale(0.95);
					}
				}
			}
		}
	}

	/* 底部模板选择区域 */
	.template-section {
		flex: 1;
		padding: 30rpx 0;
		background-color: #000;

		.template-scroll {
			width: 100%;
			height: 100%;

			.template-list {
				display: flex;
				padding: 20rpx;
				gap: 20rpx;
				// height: 100%;

				.template-item {
					flex-shrink: 0;
					width: 160rpx;
					height: 220rpx;
					background-color: #111;
					border-radius: 12rpx;
					overflow: hidden;
					transition: all 0.3s ease;
					position: relative;

					.template-image {
						width: 100%;
						height: 180rpx;
						border-radius: 12rpx 12rpx 0 0;
						background-color: #222;
					}

					.template-name {
						position: absolute;
						bottom: 0;
						left: 0;
						right: 0;
						height: 40rpx;
						background-color: rgba(0, 0, 0, 0.8);
						color: #fff;
						font-size: 22rpx;
						text-align: center;
						line-height: 40rpx;
					}

					&.active {
						transform: scale(1.05);
						border: 2rpx solid rgb(105, 229, 253);
						box-shadow: 0 0 20rpx rgba(105, 229, 253, 0.4);

						.template-name {
							background: linear-gradient(90deg, rgba(105, 229, 253, 0.8), rgba(68, 65, 253, 0.8) 100%);
							color: #fff;
							font-weight: bold;
						}
					}

					&:active {
						transform: scale(0.95);
					}
				}
			}
		}
	}

	/* 固定底部保存按钮 */
	.save-button-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 30rpx;
		background: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.8));
		backdrop-filter: blur(10rpx);

		.save-btn {
			width: 100%;
			height: 88rpx;
			background: linear-gradient(90deg, rgba(105, 229, 253, 0.8), rgba(68, 65, 253, 0.8) 100%);
			border: none;
			border-radius: 12rpx;
			box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			position: relative;
			overflow: hidden;

			.save-text {
				color: #fff;
				font-size: 32rpx;
				font-weight: bold;
				z-index: 2;
			}

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, #0056CC 0%, #003D99 100%);
				opacity: 0;
				transition: opacity 0.3s ease;
			}

			&:active {
				transform: scale(0.98);
				box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);

				&::before {
					opacity: 1;
				}
			}

			&:hover {
				box-shadow: 0 12rpx 32rpx rgba(0, 122, 255, 0.4);
				transform: translateY(-2rpx);
			}
		}
	}
</style>