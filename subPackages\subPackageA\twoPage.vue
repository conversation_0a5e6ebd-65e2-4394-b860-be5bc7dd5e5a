<template>
	<view class="a-top">
		<swiper v-if="type !== 13" :indicator-dots="true" indicator-active-color="#FFFFFF" :autoplay="true"
			:circular="true" :interval="4000" :duration="500" class="swiper">
			<swiper-item v-for="(item,index) in banner" :key="index">
				<image class="pic-img" :src="item.pic_url" @click="changeUrl(index,item)"></image>
			</swiper-item>
		</swiper>
		<template v-if="type !== 13">
			<view v-for="item in list" :key="item.id" class="list1 display-a" @click="getAdd(item)">
				<image class="img" :src="imgUrl + item.img"></image>
				<view class="center">
					<view class="title">{{item.title}}</view>
					<view class="desc">{{item.desc}}</view>
				</view>
				<view class="btn" v-if="item.id === 17">去查看</view>
				<view class="btn" v-else>去创作</view>
			</view>
		</template>
		<template v-else-if="type === 13">
			<view class="list2">
				<view v-for="(item, index) in list" @click="getAdd(item)" :key="item.id" class="list2-item"
					:style="index === 0 ? 'grid-area: item1;' : index === 1 ? 'grid-area: item2;' : 'grid-area: item3;'">
					<image class="img" :src="imgUrl + item.img"></image>
					<view class="title">{{item.title}}</view>
					<view class="desc">{{item.desc}}</view>
				</view>
			</view>
		</template>
		<!-- <view v-else class="display-fw-js">
			<view class="a-frame display-a" :style="{background: bgc}" v-for="item in list" :key="item.id"
				@click="getAdd(item)">
				<image class="img-25" :src="imgUrl + item.img"></image>
				<view>
					<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx"
						style="margin-bottom: 12rpx;">{{item.title}}
					</view>
					<view class="color_FFFFFF font-size_24rpx">{{item.desc || item.title}}</view>
				</view>
			</view>
		</view> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				imgUrl: this.$imgUrl,
				list: [],
				bgc: '',
				banner: []
			}
		},
		methods: {
			//自定义跳转
			changeUrl(index, values) {
				if (values.type == 1) {
					uni.navigateTo({
						url: values.url
					});
				}
				if (values.type == 2) {

					if (values.appid) {
						wx.navigateToMiniProgram({
							appId: values.appid,
							success: (res) => {
								// 打开成功
								// console.log('成功', res);
							},
							fail: (err) => {
								// console.log('失败', err);
							}
						})
					} else {
						this.$sun.toast("请检查跳转外部小程序的APPID是否正确", 'none');
					}
				}
			},
			//轮播图
			async getBanner() {
				const result = await this.$http.post({
					url: this.$api.banner,
					data: {
						b_type: 3
					}
				});
				if (result.errno == 0) {
					this.banner = result.data;
				}
			},
			getAdd(item) {
				switch (item.id) {
					case 1:
						uni.navigateTo({
							url: "/subPackages/subPackageA/report?type=1",
						});
						break;
					case 2:
						uni.navigateTo({
							url: "/subPackages/subPackageA/report2?type=1",
						});
						break;
					case 3:
						uni.navigateTo({
							url: "/subPackages/subPackageA/report2?type=2",
						});
						break;
					case 4:
						uni.navigateTo({
							url: "/subPackages/subPackageA/accountPackaging",
						});
						break;
					case 5:
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=1`,
						});
						break;
					case 6:
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=2`,
						});
						break;
					case 7:
						uni.navigateTo({
							url: "/pages/index/hotspot/hotspot",
						});
						break;
					case 8:
						uni.navigateTo({
							url: "/pages/index/selling/selling",
						});
						break;
					case 9:
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=15`,
						});
						break;
					case 10:
						uni.navigateTo({
							url: "/pages/index/clone/middle?type=2",
						});
						break;
					case 11:
						uni.navigateTo({
							url: "/pages/index/clone/clone",
						});
						break;
					case 12:
						uni.navigateTo({
							url: "/pages/index/clip/clip",
						});
						break;
					case 13:
						// uni.navigateTo({
						// 	url: "/pages/edit/edit",
						// });
						uni.navigateTo({
							url: "/pages/edit/selVideo?isClip=1",
						});
						break;
					case 14:
						uni.navigateTo({
							url: "/pages/edit/edit",
						});
						break;
					case 15:
						uni.navigateTo({
							url: "/pages/edit/edit",
						});
						break;
					case 16:
						uni.navigateTo({
							url: `/subPackages/subPackageA/generationsImg?title=${item.title}&type=9`,
						});
						break;
					case 17:
						uni.navigateTo({
							url: "/pages/index/AICreation/record?type=2",
						});
						break;
					default:
						break;
				}
			},
			setPagetitle(title) {
				uni.setNavigationBarTitle({
					title
				})
			}
		},
		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}
			if (options.title) {
				this.title = options.title
				this.setPagetitle(this.title)
			}

			if (this.type !== 13) {
				this.getBanner()
			}
			if (this.type === 11) {
				this.list = [{
					id: 1,
					title: 'AI立项',
					img: 'n2-1.png',
					desc: '智选赛道'
				}, {
					id: 2,
					title: 'AI诊断',
					img: 'n2-2.png',
					desc: '精准洞察'
				}, {
					id: 3,
					title: 'AI商业定位',
					img: 'n2-3.png',
					desc: '锚定价值'
				}, {
					id: 4,
					title: 'AI账号包装',
					img: 'n2-4.png',
					desc: '形象外显'
				}]
			} else if (this.type === 12) {


				if (uni.getStorageSync('videoOpen')) {
					this.list = [{
						id: 17,
						title: '历史记录',
						img: 'n2-17.png',
						desc: '深度探索，精准优化'
					}, {
						id: 16,
						title: 'DeepSeek',
						img: 'n2-16.png',
						desc: '深度探索，精准优化'
					}, {
						id: 5,
						title: '爆款选题',
						img: 'n2-5.png',
						desc: '复刻爆款视频'
					}, {
						id: 6,
						title: '文案编导',
						img: 'n2-6.png',
						desc: '自动生成脚本'
					}, {
						id: 7,
						title: '热点跟拍',
						img: 'n2-7.png',
						desc: '实时追踪热点'
					}, {
						id: 8,
						title: '文案提取',
						img: 'n2-8.png',
						desc: '形象外显'
					}, {
						id: 9,
						title: '小红书笔记',
						img: 'n2-9.png',
						desc: '生成图文笔记'
					}]
				} else {
					this.list = [{
						id: 17,
						title: '历史记录',
						img: 'n2-17.png',
						desc: '深度探索，精准优化'
					}, {
						id: 16,
						title: 'DeepSeek',
						img: 'n2-16.png',
						desc: '深度探索，精准优化'
					}, {
						id: 5,
						title: '爆款选题',
						img: 'n2-5.png',
						desc: '复刻爆款视频'
					}, {
						id: 6,
						title: '文案编导',
						img: 'n2-6.png',
						desc: '自动生成脚本'
					}, {
						id: 8,
						title: '文案提取',
						img: 'n2-8.png',
						desc: '形象外显'
					}, {
						id: 9,
						title: '小红书笔记',
						img: 'n2-9.png',
						desc: '生成图文笔记'
					}]
				}
				this.bgc = 'linear-gradient( 134deg, #5FB2FF 0%, #0050FC 100%)'
			} else if (this.type === 13) {
				this.list = [{
					id: 12,
					title: '创建数字人视频',
					img: 'n2-12.png',
					desc: '上传视频极速生成'
				}, {
					id: 10,
					title: '声音克隆',
					img: 'n2-10.png',
					desc: '仅需30秒声音复刻'
				}, {
					id: 11,
					title: '形象克隆',
					img: 'n2-11.png',
					desc: '创作你的数字人分身'
				}]
				this.bgc = 'linear-gradient(131deg, #fa8864 0%, #af4b19 100%)'
			} else if (this.type === 14) {
				this.list = [{
					id: 13,
					title: '口播精剪',
					img: 'n2-13.png',
					desc: '复刻爆款视频'
				}, {
					id: 14,
					title: '批量混剪',
					img: 'n2-14.png',
					desc: '智能合成视频'
				}, {
					id: 15,
					title: '爆款封面',
					img: 'n2-15.png',
					desc: '实时追踪热点'
				}]
				this.bgc = 'linear-gradient( 134deg, #A65DFF 0%, #696EEC 100%)'
			}
		}
	}
</script>

<style lang="scss">
	.pic-img {
		width: 710rpx;
		height: 280rpx;
		margin: 0 20rpx;
		border-radius: 10rpx;
	}

	.swiper {
		margin-bottom: 40rpx;
	}

	.list2 {
		display: grid;
		grid-template-columns: 342rpx 342rpx;
		grid-template-rows: 144rpx 144rpx;
		grid-template-areas:
			"item1 item2"
			"item1 item3";
		gap: 16rpx;
		padding: 0 20rpx;


		&-item {
			position: relative;
			border: 4rpx #d8ca94 solid;
			background: linear-gradient(134deg, #493721 0%, #2d241f 100%);
			color: #fbe598;
			border-radius: 20rpx;
			padding: 20rpx 32rpx;

			.img {
				width: 64rpx;
				height: 64rpx;
			}

			.title {
				font-size: 32rpx;
				margin-bottom: 10rpx;
			}


			.desc {
				color: #8b7b6b;
				font-size: 22rpx;
			}

			&:nth-child(1) {
				.img {
					width: 120rpx;
					height: 120rpx;
					position: absolute;
					bottom: 20rpx;
					right: 20rpx;
				}
			}

			&:nth-child(2),
			&:nth-child(3) {
				.img {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					left: 20rpx;
				}

				.title,
				.desc {
					margin-left: 68rpx;
				}
			}
		}
	}

	.list1 {
		width: 686rpx;
		background: #232325;
		padding: 48rpx;
		border-radius: 20rpx;
		margin: 0 auto;
		margin-bottom: 40rpx;

		.img {
			width: 96rpx;
			height: 96rpx;
		}

		.center {
			flex: 1;
			margin-left: 32rpx;

			.title {
				color: #FFF;
				font-size: 32rpx;
				margin-bottom: 12rpx;
			}

			.desc {
				color: rgba(255, 255, 255, .4);
				font-size: 24rpx;
			}
		}

		.btn {
			padding: 6rpx 28rpx;
			border-radius: 32rpx;
			border: 2rpx #d8ca94 solid;
			background: rgba(216, 202, 148, .2);
			color: #d8ca94;
			text-align: center;
		}
	}

	.a-top {
		margin: 20rpx 0;
		padding-bottom: 68rpx;
	}

	.display-fw-js {
		padding: 0 20rpx;
	}

	.a-frame {
		position: relative;
		width: 344rpx;
		padding: 30rpx;
		background: linear-gradient(131deg, #9CA0F4 0%, #2830DD 100%);
		border-radius: 10rpx;
		margin-bottom: 20rpx;

		.img-25 {
			position: absolute;
			right: 6rpx;
			bottom: 6rpx;
			width: 80rpx;
			height: 80rpx;
		}
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>