# 上下文
文件名：task_video_upload_upgrade.md
创建于：2025-01-08
创建者：AI Assistant
关联协议：RIPER-5 + Multidimensional + Agent Protocol (Conditional Interactive Step Review Enhanced)

# 任务描述
去除上传视频大小限制，使用uni.chooseMedia可以支持视频多选。

# 项目概述
这是一个数字人项目，包含多个视频上传功能页面。当前使用uni.chooseVideo进行单个视频选择，并有100M大小限制。需要升级为uni.chooseMedia以支持多选和去除大小限制。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
项目中发现多个使用uni.chooseVideo的页面：
1. pages/edit/videoList.vue - 主要的视频列表上传页面
2. components/sunui-upvideo/sunui-upvideo.vue - 视频上传组件
3. pages/edit/selVideo.vue - 视频选择页面
4. pages/index/clone/clone.vue - 克隆页面
5. pages/index/synthesis/synthesis.vue - 合成页面
6. pages/index/selling/selling.vue - 销售页面
7. components/sunui-upvideo1/sunui-upvideo1.vue - 另一个视频上传组件

所有页面都有100M大小限制检查，使用uni.chooseVideo单选模式。

# 提议的解决方案 (由 INNOVATE 模式填充)
采用方案1：完全替换为uni.chooseMedia
- 将uni.chooseVideo替换为uni.chooseMedia
- 支持多选视频（count: 9，iOS限制）
- 去除100M大小限制检查
- 实现队列上传逻辑
- 提供兼容性回退机制

优势：
1. 功能完整性：直接实现多选和去除大小限制
2. 代码一致性：统一使用uni.chooseMedia
3. 用户体验：更好的视频选择体验

# 实施计划 (由 PLAN 模式生成)
实施检查清单：
1. [修改pages/edit/videoList.vue中的chooseVideo方法，替换为uni.chooseMedia实现多选, review:true]
2. [更新videoList.vue中的uploadBaseVideo方法，支持多文件队列上传, review:true]
3. [修改components/sunui-upvideo/sunui-upvideo.vue组件，使用uni.chooseMedia, review:true]
4. [测试多选视频功能和大文件上传功能, review:true]
5. [可选：升级其他页面的视频选择功能, review:false]

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "1. 修改pages/edit/videoList.vue中的chooseVideo方法，替换为uni.chooseMedia实现多选" (审查需求: review:true, 状态: 初步完成)

# 任务进度 (由 EXECUTE 模式在每步完成后，以及在交互式审查迭代中追加)
*   2025-01-08
    *   步骤：1. 修改pages/edit/videoList.vue中的chooseVideo方法，替换为uni.chooseMedia实现多选 (审查需求: review:true, 状态：初步完成)
    *   修改：
        - 替换chooseVideo方法为chooseMedia，支持最多9个视频多选
        - 添加chooseVideoFallback回退方法
        - 新增uploadQueue、currentUploadIndex、totalUploadCount变量
        - 新增uploadMultipleVideos、uploadNextVideo、onAllVideosUploaded方法
        - 修改uploadBaseVideo方法支持队列上传
        - 更新UI提示文本，去除100M限制说明
        - 更新closeUploadModal方法重置新增变量
        - 根据用户反馈隐藏封面上传区域（注释掉）
        - 新增已上传视频列表展示功能，显示所有上传的视频缩略图
        - 添加formatFileSize和formatDuration格式化方法
        - 修改submitUpload方法支持批量提交多个视频
        - 添加hasUploadedVideos计算属性检查上传状态
        - 新增视频列表相关CSS样式
        - 调整视频组件尺寸：宽度280rpx，高度497rpx（9:16比例）
        - 增大字体和图标尺寸以适配更大的组件
    *   更改摘要：成功将videoList.vue从单选100M限制升级为多选无限制的视频上传功能，并根据用户反馈添加了多视频展示和隐藏封面上传功能，调整视频组件为9:16比例
    *   原因：执行计划步骤1的初步实施，并处理用户子提示"要展示所有上传的视频，将封面上传先隐藏注释掉"和"已上传的视频组件可以在大一点，比例为宽高9:16"
    *   阻碍：无
    *   用户确认状态：用户子提示迭代

# 最终审查 (由 REVIEW 模式填充)
[待填充]
